// Content script for scraping web page elements

// Track continuous scraping
let continuousScrapingInterval = null;

/**
 * Get XPath for an element
 */
const getXPath = (element) => {
  if (!element) return '';
  if (element.id) return `//*[@id="${element.id}"]`;

  let path = '';
  let current = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = current.previousElementSibling;

    while (sibling) {
      if (sibling.nodeName === current.nodeName) {
        index++;
      }
      sibling = sibling.previousElementSibling;
    }

    const tagName = current.nodeName.toLowerCase();
    path = `/${tagName}[${index}]${path}`;
    current = current.parentElement;
  }

  return path;
};

/**
 * Get CSS selector for an element
 */
const getCssSelector = (element) => {
  if (!element) return '';
  if (element.id) return `#${element.id}`;

  let selector = element.tagName.toLowerCase();

  // Handle different types of className (string, SVGAnimatedString, etc.)
  if (element.className) {
    let classStr = '';

    // Handle SVGAnimatedString or other object types
    if (typeof element.className === 'object' && element.className.baseVal !== undefined) {
      classStr = element.className.baseVal;
    }
    // Handle string className
    else if (typeof element.className === 'string') {
      classStr = element.className;
    }

    if (classStr) {
      const classes = classStr.split(' ').filter(c => c);
      if (classes.length > 0) {
        selector += `.${classes.join('.')}`;
      }
    }
  }

  return selector;
};

/**
 * Extract data from a DOM element
 */
const extractElementData = (element, depth = 0, maxDepth = 3) => {
  if (!element) return null;

  try {
    // Get element's bounding rectangle
    const rect = element.getBoundingClientRect();

    // Check if element is visible
    const style = window.getComputedStyle(element);
    const isVisible = style.display !== 'none' &&
                      style.visibility !== 'hidden' &&
                      style.opacity !== '0' &&
                      rect.width > 0 &&
                      rect.height > 0;

    // Get all attributes
    const attributes = {};
    Array.from(element.attributes).forEach(attr => {
      attributes[attr.name] = attr.value;
    });

    // Extract text content, trimming whitespace
    const text = element.textContent?.trim() || '';

    // Get className as string
    let classNameStr = '';
    if (element.className) {
      // Handle SVGAnimatedString or other object types
      if (typeof element.className === 'object' && element.className.baseVal !== undefined) {
        classNameStr = element.className.baseVal;
      }
      // Handle string className
      else if (typeof element.className === 'string') {
        classNameStr = element.className;
      }
    }

    // Create element data object
    const data = {
      tagName: element.tagName.toLowerCase(),
      id: element.id || '',
      className: classNameStr,
      text: text,
      attributes: attributes,
      xpath: getXPath(element),
      cssSelector: getCssSelector(element),
      rect: {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      },
      isVisible: isVisible,
      timestamp: new Date().toISOString(),
      children: []
    };

    // Process children if we haven't reached max depth
    if (depth < maxDepth) {
      Array.from(element.children).forEach(child => {
        const childData = extractElementData(child, depth + 1, maxDepth);
        if (childData) {
          data.children.push(childData);
        }
      });
    }

    return data;
  } catch (error) {
    console.error('Error extracting element data:', error);
    return null;
  }
};

/**
 * Get all visible elements on the page
 */
const getAllVisibleElements = () => {
  // Get all elements in the document
  const allElements = document.querySelectorAll('*');
  const visibleElements = [];

  // Filter for visible elements
  allElements.forEach(element => {
    // Skip script, style, meta, and other non-visible elements
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'script' || tagName === 'style' || tagName === 'meta' ||
        tagName === 'head' || tagName === 'link' || tagName === 'noscript') {
      return;
    }

    // Check if element is visible
    const style = window.getComputedStyle(element);
    const isVisible = style.display !== 'none' &&
                      style.visibility !== 'hidden' &&
                      style.opacity !== '0' &&
                      element.getBoundingClientRect().width > 0 &&
                      element.getBoundingClientRect().height > 0;

    if (isVisible) {
      visibleElements.push(element);
    }
  });

  return visibleElements;
};

/**
 * Get all text nodes that are directly visible (not just container elements)
 */
const getVisibleTextElements = () => {
  const allElements = document.querySelectorAll('*');
  const textElements = [];

  allElements.forEach(element => {
    // Skip non-visible elements
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
      return;
    }

    // Check if this element has direct text content (not just from children)
    const hasDirectText = Array.from(element.childNodes)
      .some(node => node.nodeType === Node.TEXT_NODE && node.textContent?.trim());

    if (hasDirectText) {
      textElements.push(element);
    }
  });

  return textElements;
};

/**
 * Scrape all elements from the page
 * @param maxDepth Maximum depth to traverse the DOM
 */
const scrapeAllElements = (maxDepth = 3) => {
  // Get all visible elements
  const visibleElements = getAllVisibleElements();
  // Get elements with direct text content
  const textElements = getVisibleTextElements();

  // Combine and deduplicate
  const uniqueElements = Array.from(new Set([...visibleElements, ...textElements]));
  const scrapedData = [];

  // Process each element separately
  uniqueElements.forEach(element => {
    // Skip elements that are likely part of the extension UI
    if (element.id && (
        element.id.startsWith('quickadapt-') ||
        element.id.startsWith('qadpt-') ||
        element.id.includes('my-react-drawer'))) {
      return;
    }

    // Skip elements with certain classes that might be part of the extension
    let classStr = '';
    if (element.className) {
      // Handle SVGAnimatedString or other object types
      if (typeof element.className === 'object' && element.className.baseVal !== undefined) {
        classStr = element.className.baseVal;
      }
      // Handle string className
      else if (typeof element.className === 'string') {
        classStr = element.className;
      }

      if (classStr && (
          classStr.includes('quickadapt') ||
          classStr.includes('qadpt'))) {
        return;
      }
    }

    const elementData = extractElementData(element, 0, maxDepth);
    if (elementData) {
      // Only include elements that have some content (text, id, class, etc.)
      if (elementData.text || elementData.id || elementData.className ||
          Object.keys(elementData.attributes).length > 0) {
        // Store each element separately
        scrapedData.push(elementData);
      }
    }
  });

  // Create the final data object
  return {
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString(),
    elements: scrapedData
  };
};

/**
 * Start scraping the page
 * @param maxDepth Maximum depth to traverse the DOM
 */
const startScraping = (maxDepth = 3) => {
  return new Promise((resolve) => {
    // Use requestAnimationFrame to avoid blocking the UI
    requestAnimationFrame(() => {
      const data = scrapeAllElements(maxDepth);
      resolve(data);
    });
  });
};

/**
 * Start continuous scraping
 * @param interval Interval in milliseconds between scrapes
 * @param maxDepth Maximum depth to traverse the DOM
 */
const startContinuousScraping = (interval = 5000, maxDepth = 3) => {
  // Clear any existing interval
  if (continuousScrapingInterval !== null) {
    window.clearInterval(continuousScrapingInterval);
  }

  // Create a notification to show scraping is active
  createScrapingNotification();

  // Do an initial scrape
  startScraping(maxDepth).then(data => {
    // Send initial data
    chrome.runtime.sendMessage({
      action: 'scrapingComplete',
      data: data,
      continuous: true,
      append: false
    });

    // Set up interval for continuous scraping
    continuousScrapingInterval = window.setInterval(() => {
      startScraping(maxDepth).then(newData => {
        // Send updated data
        chrome.runtime.sendMessage({
          action: 'scrapingComplete',
          data: newData,
          continuous: true,
          append: true
        });

        // Update the notification
        updateScrapingNotification();
      });
    }, interval);
  });
};

/**
 * Create a notification element to show scraping is active
 */
const createScrapingNotification = () => {
  // Remove any existing notification
  const existingNotification = document.getElementById('quickadapt-scraping-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create a new notification element
  const notification = document.createElement('div');
  notification.id = 'quickadapt-scraping-notification';
  notification.style.cssText = `
    position: fixed;
    bottom: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    pointer-events: none;
    transition: opacity 0.3s;
  `;
  notification.textContent = 'QuickAdapt: Scraping active... ' + new Date().toLocaleTimeString();

  // Add to the document
  document.body.appendChild(notification);

  // Fade out after 3 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
  }, 3000);
};

/**
 * Update the scraping notification
 */
const updateScrapingNotification = () => {
  const notification = document.getElementById('quickadapt-scraping-notification');
  if (notification) {
    notification.textContent = 'QuickAdapt: Scraping active... ' + new Date().toLocaleTimeString();
    notification.style.opacity = '1';

    // Fade out after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
    }, 3000);
  }
};

/**
 * Stop continuous scraping
 */
const stopContinuousScraping = () => {
  if (continuousScrapingInterval !== null) {
    window.clearInterval(continuousScrapingInterval);
    continuousScrapingInterval = null;
  }

  // Remove the notification
  const notification = document.getElementById('quickadapt-scraping-notification');
  if (notification) {
    notification.remove();
  }

  // Send a message that scraping has stopped
  chrome.runtime.sendMessage({
    action: 'scrapingStopped'
  });
};

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Handle scraping
  if (message.action === 'startScraping') {
    console.log('Starting scraping process...');

    // Extract options from message
    const maxDepth = message.maxDepth || 3;

    // Always start with a single scrape
    startScraping(maxDepth).then(data => {
      // Send initial scraped data back to the extension
      chrome.runtime.sendMessage({
        action: 'scrapingComplete',
        data: data,
        continuous: false,
        append: false
      });

      // Then automatically start continuous scraping
      startContinuousScraping(5000, maxDepth);

      sendResponse({ success: true });
    });

    return true; // Keep the message channel open for async response
  }

  // Handle stopping scraping
  if (message.action === 'stopScraping') {
    console.log('Stopping scraping process...');

    stopContinuousScraping();
    sendResponse({ success: true });

    return true;
  }
});

// Add event listeners for user interactions to detect changes
const userInteractionEvents = ['click', 'input', 'change', 'keyup', 'scroll'];

// Track when the last interaction happened
let lastInteractionTime = Date.now();
let pendingInteractionScrape = false;

userInteractionEvents.forEach(eventType => {
  document.addEventListener(eventType, (event) => {
    // If continuous scraping is active, trigger an immediate scrape after user interaction
    if (continuousScrapingInterval !== null) {
      lastInteractionTime = Date.now();

      // Check if the interaction might have revealed new elements (like expanding a dropdown)
      const target = event.target;
      if (target && (
          eventType === 'click' ||
          (target.tagName && (
            target.tagName.toLowerCase() === 'select' ||
            target.tagName.toLowerCase() === 'button' ||
            target.tagName.toLowerCase() === 'a'
          ))
        )) {

        // Don't trigger too many scrapes in succession
        if (!pendingInteractionScrape) {
          pendingInteractionScrape = true;

          // Wait a short time for the DOM to update
          setTimeout(() => {
            // Only scrape if we're still scraping
            if (continuousScrapingInterval !== null) {
              console.log(`User interaction (${eventType}) detected, scraping now`);
              startScraping(3).then(newData => {
                chrome.runtime.sendMessage({
                  action: 'scrapingComplete',
                  data: newData,
                  continuous: true,
                  append: true,
                  interactionTriggered: true
                });
                updateScrapingNotification();
              });
            }
            pendingInteractionScrape = false;
          }, 500); // Wait 500ms for DOM updates
        }
      }
    }
  });
});

// Log that the scraper has been loaded
console.log('QuickAdapt scraper content script loaded');
