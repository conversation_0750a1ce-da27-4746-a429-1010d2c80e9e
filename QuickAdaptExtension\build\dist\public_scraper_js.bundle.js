"use strict";
(self["webpackChunkuserguide"] = self["webpackChunkuserguide"] || []).push([["public_scraper_js"],{

/***/ "./public/scraper.js":
/*!***************************!*\
  !*** ./public/scraper.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   scrapeDOM: () => (/* binding */ scrapeDOM)
/* harmony export */ });
// Scraper module for content.js
// This file exports the scraping functionality for the content script

/**
 * Get XPath for an element
 */
const generateXPath = element => {
  if (!element) return '';
  if (element.id) return `//*[@id="${element.id}"]`;
  let path = '';
  let current = element;
  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = current.previousElementSibling;
    while (sibling) {
      if (sibling.nodeName === current.nodeName) {
        index++;
      }
      sibling = sibling.previousElementSibling;
    }
    const tagName = current.nodeName.toLowerCase();
    path = `/${tagName}[${index}]${path}`;
    current = current.parentElement;
  }
  return path;
};

/**
 * Get CSS selector for an element
 */
const generateCSSSelector = element => {
  if (!element) return '';
  if (element.id) return `#${element.id}`;
  let selector = element.tagName.toLowerCase();

  // Handle different types of className (string, SVGAnimatedString, etc.)
  if (element.className) {
    let classStr = '';

    // Handle SVGAnimatedString or other object types
    if (typeof element.className === 'object' && element.className.baseVal !== undefined) {
      classStr = element.className.baseVal;
    }
    // Handle string className
    else if (typeof element.className === 'string') {
      classStr = element.className;
    }
    if (classStr) {
      const classes = classStr.split(' ').filter(c => c);
      if (classes.length > 0) {
        selector += `.${classes.join('.')}`;
      }
    }
  }
  return selector;
};

/**
 * Check if an element is visible
 */
function isVisible(element) {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0' && element.offsetParent !== null;
}

/**
 * Recursively scrape the DOM and collect content up to a given depth
 */
function scrapeDOM() {
  let maxDepth = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 3;
  const results = [];
  const excludedTags = ['SCRIPT', 'STYLE', 'NOSCRIPT', 'IFRAME'];
  function traverse(node, depth) {
    if (depth > maxDepth) return;
    if (node.nodeType !== Node.ELEMENT_NODE || excludedTags.includes(node.tagName)) return;
    const el = node;
    if (!isVisible(el)) return;
    const tag = el.tagName.toLowerCase();
    const attributes = {};
    for (let attr of Array.from(el.attributes)) {
      attributes[attr.name] = attr.value;
    }
    let text = '';
    if (tag === 'input' || tag === 'textarea') {
      text = el.value || '';
    } else if (tag === 'select') {
      const selected = el.selectedOptions[0];
      text = selected?.text || '';
    } else {
      text = el.innerText?.trim();
    }
    if (text) {
      results.push({
        tag,
        text,
        attributes,
        xpath: generateXPath(el),
        cssPath: generateCSSSelector(el)
      });
    }
    for (let child of Array.from(el.children)) {
      traverse(child, depth + 1);
    }
  }
  traverse(document.body, 0);
  return Promise.resolve(results);
}

// Export the scraping functions
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  scrapeDOM
});

/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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