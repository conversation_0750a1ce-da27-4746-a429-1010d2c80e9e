import React from 'react';
import { createRoot } from 'react-dom/client';
import Drawer from '../src/components/drawer/Drawer';
import App from '../src/App'
import { initScraper } from '../src/services/scraper';

// Function to create and show the popup loader
function showPopupLoader() {
  console.log('🔄 QuickAdapt: Showing popup loader...');

  // Check if loader already exists
  if (document.getElementById('qadpt-popup-loader')) {
    console.log('⚠️ QuickAdapt: Loader already exists');
    return;
  }

  // Create loader HTML with GIF
  const loaderHTML = `
    <div id="qadpt-popup-loader" class="qadpt-popup-loader">
      <div class="qadpt-loader-container">
        <div class="qadpt-loader-gif">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="#5F9EA0" opacity="0.3"/>
            <path d="M12 2C13.3132 2 14.6136 2.25866 15.8268 2.7612C17.0401 3.26375 18.1425 4.00035 19.0711 4.92893C19.9997 5.85752 20.7362 6.95991 21.2388 8.17317C21.7413 9.38642 22 10.6868 22 12" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round">
              <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/>
            </path>
          </svg>
        </div>
        <div class="qadpt-loader-text">QuickAdapt</div>
      </div>
    </div>
  `;

  // Create loader element
  const loaderElement = document.createElement('div');
  loaderElement.innerHTML = loaderHTML;
  const loader = loaderElement.firstElementChild;

  // Add CSS styles directly to avoid dependency issues
  const loaderStyles = `
    .qadpt-popup-loader {
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      z-index: 2147483647 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
      pointer-events: none !important;
      opacity: 1 !important;
      visibility: visible !important;
    }
    .qadpt-loader-container {
      background-color: #ffffff !important;
      border: 1px solid #e0e0e0 !important;
      border-radius: 12px !important;
      padding: 16px 20px !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
      display: flex !important;
      align-items: center !important;
      gap: 12px !important;
      min-width: 140px !important;
      max-width: 220px !important;
      animation: qadpt-loader-fadeIn 0.3s ease-out !important;
      transform: translateZ(0) !important;
    }
    .qadpt-loader-gif {
      width: 24px !important;
      height: 24px !important;
      flex-shrink: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
    .qadpt-loader-gif svg {
      width: 24px !important;
      height: 24px !important;
      display: block !important;
    }
    .qadpt-loader-text {
      font-size: 13px !important;
      font-weight: 600 !important;
      color: #5F9EA0 !important;
      white-space: nowrap !important;
      letter-spacing: 0.5px !important;
    }
    @keyframes qadpt-loader-fadeIn {
      0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }
    @keyframes qadpt-loader-fadeOut {
      0% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
      100% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
    }
    .qadpt-popup-loader.fade-out .qadpt-loader-container {
      animation: qadpt-loader-fadeOut 0.3s ease-in forwards !important;
    }
    @media (max-width: 768px) {
      .qadpt-popup-loader {
        top: 15px !important;
        right: 15px !important;
      }
      .qadpt-loader-container {
        padding: 12px 16px !important;
        min-width: 120px !important;
      }
      .qadpt-loader-gif {
        width: 20px !important;
        height: 20px !important;
      }
      .qadpt-loader-gif svg {
        width: 20px !important;
        height: 20px !important;
      }
      .qadpt-loader-text {
        font-size: 12px !important;
      }
    }
    .rtl .qadpt-popup-loader {
      right: auto !important;
      left: 20px !important;
    }
    @media (max-width: 768px) {
      .rtl .qadpt-popup-loader {
        left: 15px !important;
      }
    }
  `;

  // Add styles to head if not already present
  if (!document.getElementById('qadpt-loader-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'qadpt-loader-styles';
    styleElement.textContent = loaderStyles;
    document.head.appendChild(styleElement);
  }

  // Add loader to page
  document.body.appendChild(loader);

  console.log('✅ QuickAdapt: Popup loader added to page');
  console.log('📍 QuickAdapt: Loader element:', loader);
  console.log('📍 QuickAdapt: Loader position:', {
    top: loader.style.top || 'from CSS',
    right: loader.style.right || 'from CSS',
    zIndex: loader.style.zIndex || 'from CSS'
  });
}

// Function to hide the popup loader
function hidePopupLoader() {
  console.log('🔄 QuickAdapt: Hiding popup loader...');

  const loader = document.getElementById('qadpt-popup-loader');
  if (loader) {
    console.log('✅ QuickAdapt: Found loader, starting fade-out');
    loader.classList.add('fade-out');
    setTimeout(() => {
      if (loader.parentNode) {
        loader.parentNode.removeChild(loader);
        console.log('✅ QuickAdapt: Popup loader removed');
      }
      // Also remove styles
      const styles = document.getElementById('qadpt-loader-styles');
      if (styles && styles.parentNode) {
        styles.parentNode.removeChild(styles);
        console.log('✅ QuickAdapt: Loader styles removed');
      }
    }, 300); // Match the fade-out animation duration
  } else {
    console.log('⚠️ QuickAdapt: No loader found to hide');
  }
}

// Check if our app container already exists
const existingContainer = document.getElementById('my-react-drawer');
if (!existingContainer) {
  // Only create the container if it doesn't exist
  const appContainer = document.createElement('div');
  appContainer.id = 'my-react-drawer';
  document.body.appendChild(appContainer);

  // Check if we should render on this site
  const restrictedSites = ["user.quickadopt.in", "web.quickadopt.in", "google.com", "127.0.0.1"];
  const currentSite = window.location.hostname;

  if (!restrictedSites.some(site => currentSite.includes(site))) {
    // Check if this tab should have the extension open
    chrome.runtime.sendMessage({ action: "checkTabStatus" }, (response) => {
      if (response && response.shouldOpen) {
        // Show the popup loader immediately
        showPopupLoader();

        // Set up listener for when the login page is ready
        const handleLoginReady = () => {
          console.log('🔐 QuickAdapt: Login page ready, hiding loader');
          hidePopupLoader();
          window.removeEventListener('quickadapt-login-ready', handleLoginReady);
        };
        window.addEventListener('quickadapt-login-ready', handleLoginReady);

        // Set up listener for when user is already logged in (app ready)
        const handleAppReady = () => {
          console.log('✅ QuickAdapt: App ready (user logged in), hiding loader');
          hidePopupLoader();
          window.removeEventListener('quickadapt-app-ready', handleAppReady);
          window.removeEventListener('quickadapt-login-ready', handleLoginReady);
        };
        window.addEventListener('quickadapt-app-ready', handleAppReady);

        // Fallback timeout in case events don't fire (safety net)
        const fallbackTimeout = setTimeout(() => {
          console.log('⏰ QuickAdapt: Fallback timeout reached, hiding loader');
          hidePopupLoader();
          window.removeEventListener('quickadapt-app-ready', handleAppReady);
          window.removeEventListener('quickadapt-login-ready', handleLoginReady);
        }, 10000); // 10 second fallback for safety

        // Now get the state and render
        chrome.runtime.sendMessage({ action: "getState" }, (stateResponse) => {
          const savedState = stateResponse ? stateResponse.state : null;

          const root = createRoot(document.getElementById('my-react-drawer'));
          root.render(<App initialState={savedState} />);
        });

        // Set up event listener for beforeunload to save state
        window.addEventListener('beforeunload', () => {
          if (window.appState) {
            chrome.runtime.sendMessage({
              action: "saveState",
              state: window.appState
            });
          }
        });
        initScraper();

      }
      else {
        // Remove the container if extension shouldn't be open in this tab
        appContainer.remove();
      }
    });

    let observer = null;
    let isScraping = false;

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'startClickScraping') {
        isScraping = true;
        console.log('Click-based scraping started in content script');
        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'stopClickScraping') {
        isScraping = false;
        console.log('Click-based scraping stopped in content script');
        sendResponse({ success: true });
        return true;
      }

      // Legacy support for old scraping methods
      if (message.action === 'startScraping') {
        isScraping = true;
         if (observer) observer.disconnect();
        import('./scraper').then(({ scrapeDOM }) => {
          scrapeDOM(message.maxDepth).then(data => {
            chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
          });
        });

        // Start observing DOM changes
        if (message.observeMutations) {
          startDOMObserver(message.maxDepth);
        }

        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'stopScraping') {
        if (observer) {
          observer.disconnect();
          observer = null;
        }
        isScraping = false;
        sendResponse({ success: true });
        return true;
      }
    });

    // Add event listeners for custom events as fallback
    window.addEventListener('quickadapt-start-click-scraping', () => {
      isScraping = true;
      console.log('Click-based scraping started via custom event');
    });

    window.addEventListener('quickadapt-stop-click-scraping', () => {
      isScraping = false;
      console.log('Click-based scraping stopped via custom event');
    });

    // Legacy event listeners for backward compatibility
    window.addEventListener('quickadapt-start-scraping', (event) => {
      const detail = event.detail || {};
      const maxDepth = detail.maxDepth || 3;

      isScraping = true;
      if (observer) observer.disconnect();

      import('./scraper').then(({ scrapeDOM }) => {
        scrapeDOM(maxDepth).then(data => {
          chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
        });
      });

      // Start observing DOM changes
      if (detail.observeMutations) {
        startDOMObserver(maxDepth);
      }
    });

    window.addEventListener('quickadapt-stop-scraping', () => {
      if (observer) {
        observer.disconnect();
        observer = null;
      }
      isScraping = false;
    });


    function startDOMObserver(maxDepth = 3){
      if (observer) observer.disconnect(); // Clear existing

      observer = new MutationObserver((mutations) => {
        let shouldScrape = false;

        for (const mutation of mutations) {
          if (
            mutation.type === 'childList' &&
            (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)
          ) {
            shouldScrape = true;
            break;
          }


        if (
          mutation.type === 'attributes' &&
          (mutation.attributeName === 'style' || mutation.attributeName === 'class')
        ) {
          shouldScrape = true;
          break;
        }
      }
        if (shouldScrape) {
          if (scrapeTimeout) clearTimeout(scrapeTimeout);

          // Debounce: wait 300ms after last mutation
          scrapeTimeout = setTimeout(() => {
            import('./scraper').then(({ scrapeDOM }) => {
              scrapeDOM(maxDepth).then(data => {
                chrome.runtime.sendMessage({ action: 'startScraping', data, append: true });
              });
            });
          }, 300);
        }
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true
      });
    }




    function removeExtensionContainer() {
      const container = document.getElementById('my-react-drawer');
      if (container) {
        container.remove();
      }
    }


    // Send message to background script to mark tab as closed
    function closeExtension() {
      chrome.runtime.sendMessage({ action: "closeExtension" }, (response) => {
        if (response && response.success) {
          removeExtensionContainer();
        }
      });
    }
       (function() {
  const htmlDir = document.documentElement.getAttribute('dir');
  const bodyDir = document.body.getAttribute('dir');
  if (htmlDir === 'rtl' || bodyDir === 'rtl') {
    document.body.classList.add('rtl');
  }
})();
    // You can attach this to a global window object if needed
    window.closeExtension = closeExtension;
    window.hideQuickAdaptLoader = hidePopupLoader;
    window.showQuickAdaptLoader = showPopupLoader;

    // Test function to create a very visible popup
    window.testQuickAdaptLoader = function() {
      console.log('🧪 Testing QuickAdapt loader...');

      // Remove any existing loader first
      const existing = document.getElementById('qadpt-popup-loader');
      if (existing) existing.remove();

      // Create a very visible test loader with GIF
      const testLoader = document.createElement('div');
      testLoader.id = 'qadpt-popup-loader';
      testLoader.innerHTML = `
        <div style="
          position: fixed !important;
          top: 20px !important;
          right: 20px !important;
          z-index: 2147483647 !important;
          background: #ffffff !important;
          color: #5F9EA0 !important;
          padding: 20px !important;
          border-radius: 12px !important;
          font-family: Arial, sans-serif !important;
          font-size: 14px !important;
          font-weight: 600 !important;
          box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
          border: 2px solid #5F9EA0 !important;
          min-width: 160px !important;
          text-align: center !important;
          display: flex !important;
          align-items: center !important;
          gap: 12px !important;
        ">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="#5F9EA0" opacity="0.3"/>
            <path d="M12 2C13.3132 2 14.6136 2.25866 15.8268 2.7612C17.0401 3.26375 18.1425 4.00035 19.0711 4.92893C19.9997 5.85752 20.7362 6.95991 21.2388 8.17317C21.7413 9.38642 22 10.6868 22 12" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round">
              <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/>
            </path>
          </svg>
          <span>QuickAdapt Loading...</span>
        </div>
      `;

      document.body.appendChild(testLoader);
      console.log('🧪 Test loader created and added to page');

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (testLoader.parentNode) {
          testLoader.parentNode.removeChild(testLoader);
          console.log('🧪 Test loader removed');
        }
      }, 5000);
    };

  } else {
    // Remove the container on restricted sites
    appContainer.remove();
    console.log("Extension restricted on this site.");
  }
} else {
  console.log("QuickAdopt already initialized on this page.");
}
// === Inject invisible marker for extension detection ===
const quickadaptMarkerId = 'quickadapt-extension-marker';

// --- BEGIN: Handle quickadopt_guide_id param ---
(function handleQuickAdoptGuideId() {
  try {
    const url = new URL(window.location.href);
    const guideId = url.searchParams.get('quickadopt_guide_id');
    if (guideId) {
      sessionStorage.setItem('pending_guide_id', guideId);
      url.searchParams.delete('quickadopt_guide_id');
      window.history.replaceState({}, document.title, url.toString());
      // Optionally, send a custom event to notify React app
      window.dispatchEvent(new CustomEvent('quickadopt-guide-id-detected', { detail: { guideId } }));
    }
  } catch (e) { /* ignore */ }
})();
// --- END: Handle quickadopt_guide_id param ---

if (!document.getElementById(quickadaptMarkerId)) {
  const markerElement = document.createElement('div');
  markerElement.id = quickadaptMarkerId;
  markerElement.style.display = 'none';
  document.documentElement.appendChild(markerElement);
}

