import React, { useContext } from 'react';
import { stopScraping } from '../../services/ScrapingService';
import './EnableAIButton.css';
import { AccountContext } from '../../components/login/AccountContext';
import { useTranslation } from 'react-i18next';
interface StopScrapingButtonProps {
  onClick: () => void;
}

const StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {
  const { accountId } = useContext(AccountContext);
  const { t: translate } = useTranslation()
  const handleClick = async () => {
    
    stopScraping(accountId);
    onClick();
  };
  
  ;
  
  

  return (
    <div className='stop-scraping-button-container' id='stop-scraping-button'>
      <button className="enable-ai-button stop-scraping-button" onClick={handleClick}>
        <span className="enable-ai-text">{translate("Stop Training")}</span>
      </button>
    </div>
  );
};

export default StopScrapingButton;