import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from "react";
import { Box, Popover, Typography, IconButton } from "@mui/material";
import JoditEditor from "jodit-react";

import RTE from "./RTE";
import useDrawerStore, { IRTEContainer, TSectionType } from "../../../../store/drawerStore";
import { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { copyicon, deleteicon } from "../../../../assets/icons/icons";
import { useTranslation } from 'react-i18next';

interface RTEsectionProps {
	items: IRTEContainer;
	boxRef: React.RefObject<HTMLDivElement>;
	handleFocus: (id: string) => void;
	handleeBlur: (id: string) => void;

	isPopoverOpen: boolean;
	setIsPopoverOpen: (params: boolean) => void;
	currentRTEFocusedId: string;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
	(
		{
			items: { id, rteBoxValue },
			boxRef,
			handleFocus,
			handleeBlur,

			isPopoverOpen,
			setIsPopoverOpen,
			currentRTEFocusedId,
		},
		ref
	) => {
		const { t: translate } = useTranslation();
		const {
			setIsUnSavedChanges,
			setHtmlContent,
			textvaluess,
			setTextvaluess,
			backgroundC,
			setBackgroundC,
			Bbordercolor,
			BborderSize,
			bpadding,
			sectionColor,
			setSectionColor,
			handleTooltipRTEBlur,
			handleTooltipRTEValue,
			handleRTEDeleteSection,
			handleRTECloneSection,
			tooltip,
			currentStep,
			toolTipGuideMetaData,
		} = useDrawerStore((state) => state);
		// Removed unused state variables since we're using Jodit editor directly



		// Memoize Jodit config to prevent re-renders and focus loss
		const joditConfig = useMemo((): any => ({
			readonly: false,
			// Hide main toolbar by default
			toolbar: false,
			// Enable inline toolbar for text selection
			toolbarInline: true,
			toolbarInlineForSelection: true,
			toolbarInlineDisabledButtons: ['source', 'fullsize'],
			toolbarInlineDisableFor: [],
			toolbarSticky: false,
			toolbarAdaptive: false,
			// Inline toolbar width configuration
			toolbarButtonSize: 'small',
			toolbarInlineWidth: 500,
			toolbarInlineMaxWidth: 600,
			toolbarInlineMinWidth: 450,
			// Additional popup configuration for inline toolbar
			popup: {
				selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],
				toolbar: {
					width: 500,
					maxWidth: 600,
					minWidth: 450
				}
			},
			showCharsCounter: false,
			showWordsCounter: false,
			showXPathInStatusbar: false,
			statusbar: false,
			pastePlain: true,
			askBeforePasteHTML: false,
			askBeforePasteFromWord: false,
			buttons: [
				'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
				'font', 'fontsize', 'link',
				{
					name: 'more',
					iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
					list: [
						'image', 'video', 'table',
						'align', 'undo', 'redo', '|',
						'hr', 'eraser', 'copyformat',
						'symbol', 'print', 'superscript', 'subscript', '|',
						'outdent', 'indent', 'paragraph',
					]
				}
			],
			autofocus: false,
			// Enable auto-resize behavior
			height: 'auto',
			minHeight: 28,
			maxHeight: 112,
			// Fix dialog positioning by setting popup root to document body
			popupRoot: document.body,
			// Ensure dialogs appear in correct position
			zIndex: 100000,
			globalFullSize: false,
			// Add custom CSS to ensure text is visible
			style: {
				color: '#000000 !important',
				backgroundColor: '#ffffff',
				fontFamily: 'Poppins, sans-serif',
			},
			// Override editor styles to ensure text visibility
			editorCssClass: 'jodit-tooltip-editor',
			// Set default content styling
			enter: 'p' as const,
			// Fix link dialog positioning
			link: {
				followOnDblClick: false,
				processVideoLink: true,
				processPastedLink: true,
				openInNewTabCheckbox: true,
				noFollowCheckbox: false,
				modeClassName: 'input' as const,
			},
			// Dialog configuration
			dialog: {
				zIndex: 100001,
			},
			controls: {
				font: {
					list: {
						"Poppins, sans-serif": "Poppins",
						"Roboto, sans-serif": "Roboto",
						"Comic Sans MS, sans-serif": "Comic Sans MS",
						"Open Sans, sans-serif": "Open Sans",
						"Calibri, sans-serif": "Calibri",
						"Century Gothic, sans-serif": "Century Gothic",
					}
				}
			}
		}), [id]);

		// Memoize onChange handler to prevent re-renders
		const handleContentChange = useCallback((newContent: string) => {
			handleTooltipRTEValue(id, newContent);
		}, [id, handleTooltipRTEValue]);
		const [isEditing, setIsEditing] = useState(false);
		const editorRef = useRef(null);
		const containerRef = useRef<HTMLDivElement | null>(null);

		// const handleInput = () => {
		// 	// Update the content state when user types
		// 	if (boxRef.current) {
		// 		const updatedContent = boxRef.current.innerHTML;
		// 		setContent(updatedContent); // Store the content in state
		// 		setHtmlContent(updatedContent); // Update the HTML content
		// 		setIsUnSavedChanges(true);
		// 		preserveCaretPosition();
		// 	}
		// };
		// Removed caret position functions since we're using Jodit editor

		// useEffect(() => {
		// 	// After content update, restore the cursor position
		// 	restoreCaretPosition();
		// }, [boxRef.current?.innerHTML]); // Run when content changes

		// Remove section

		// useEffect(() => {
		// 	if (boxRef.current?.innerHTML?.trim()) {
		// 		setIsUnSavedChanges(true);
		// 	}
		// }, [boxRef.current?.innerHTML?.trim()]);

		// Removed useEffect since we're using Jodit editor directly

		// Auto-focus the editor when editing mode is activated
		useEffect(() => {
			if (isEditing && editorRef.current) {
				setTimeout(() => {
					(editorRef.current as any).editor.focus();
				}, 50);
			}
		}, [isEditing]);

		// Handle clicks outside the editor to close editing mode
		useEffect(() => {
			const handleClickOutside = (event: MouseEvent) => {
				const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
				const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
				const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
				const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
				const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
				const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
				const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
				const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
				const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

				// Check if the target is inside the editor or related elements
				if (
					containerRef.current &&
					!containerRef.current.contains(event.target as Node) && // Click outside the editor container
					!isInsidePopup && // Click outside the popup
					!isInsideJoditPopup && // Click outside the WYSIWYG editor
					!isInsideWorkplacePopup && // Click outside the workplace popup
					!isSelectionMarker && // Click outside selection markers
					!isLinkPopup && // Click outside link input popup
					!isInsideToolbarButton &&// Click outside the toolbar button
					!isInsertButton &&
					!isInsideJoditPopupContent &&
					!isInsideAltTextPopup
				) {
					setIsEditing(false); // Close the editor if clicked outside
				}
			};

			if (isEditing) {
				document.addEventListener("mousedown", handleClickOutside);
				return () => document.removeEventListener("mousedown", handleClickOutside);
			}
		}, [isEditing]);

		return (
			<>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						position: "relative",
						//padding: 0,
						margin: 0,
						boxSizing: "border-box",
						transition: "border 0.2s ease-in-out",
						backgroundColor: sectionColor || "defaultColor",
						//border: `${BborderSize}px solid ${Bbordercolor} !important` || "defaultColor",
						// padding: `${bpadding}px !important` || "0",
					}}
					className="qadpt-rte"
					id="rte-box"
				>
					{/* RTE Container with hover icons */}
					<div
						style={{
							width: "100%",
							position: "relative",
							color: "#000000",
							backgroundColor: "#ffffff"
						}}
						className="rte-container"
					>
						{/* Action Icons - positioned at top right, visible only on hover */}
						<div
							className="rte-action-icons"
							style={{
								position: "absolute",
								top: "8px",
								right: "15px",
								zIndex: 1000,
								display: "flex",
								opacity: 0,
								transition: "opacity 0.2s ease-in-out",
								backgroundColor: "rgba(255, 255, 255, 0.9)",
								borderRadius: "4px",
								padding: "2px",
								boxShadow: "rgba(0, 0, 0, 0.1) 1px 1px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
							}}
						>
							<IconButton
								size="small"
								onClick={() => handleRTECloneSection(id)}
								disabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3}
								title={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? translate("Maximum limit of 3 Rich Text sections reached") : translate("Clone Section")}
								sx={{
									width: "24px",
									height: "24px",
									padding: "2px",
									"&:hover": {
										backgroundColor: "rgba(var(--primarycolor-rgb), 0.1) !important",
									},
									svg: {
										height: "14px",
										width: "14px",
										path: {
											fill:"var(--primarycolor)"
										}
									},
								}}
							>
								<span
									dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{
										opacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? 0.5 : 1,
										height: '12px',
										width: '12px',
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center'
									}}
								/>
							</IconButton>
							<IconButton
								size="small"
								onClick={() => handleRTEDeleteSection(id)}
								disabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}
								title={translate("Delete Section")}
								sx={{
									width: "24px",
									height: "24px",
									padding: "2px",
									"&:hover": {
										backgroundColor: "rgba(255, 0, 0, 0.1) !important",
									},
									svg: {
										height: "14px",
										width: "14px",
										path: {
											fill:"#ff4444"
										}
									},
								}}
							>
								<span
									dangerouslySetInnerHTML={{ __html: deleteicon }}
									style={{
										opacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,
										height: '14px',
										width: '14px',
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center'
									}}
								/>
							</IconButton>
						</div>
							<style>
								{`
										/* Hide the add new line button */
										.jodit-add-new-line {
											display: none !important;
										}
									/* Tooltip/Hotspot specific Jodit editor styles */
									.jodit-tooltip-editor .jodit-wysiwyg {
										color: #000000 !important;
										background-color: #ffffff !important;
										line-height: 1.4 !important;
										padding: 8px !important;
									}
									/* Height and scrolling behavior for tooltip RTE */
									.jodit-tooltip-editor .jodit-workplace {
										min-height: 28px !important;
										max-height: 112px !important;
										overflow-y: auto !important;
										line-height: 1.4 !important;
									}
									.jodit-tooltip-editor .jodit-container {
										minHeight: "28px !important",
										border: "none !important"
									}
									/* Target the specific jodit container class combination */
									.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {
										border: 0 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg p {
										color: #000000 !important;
										margin: 0 0 4px 0 !important;
										line-height: 1.4 !important;
									}
									/* Enhanced scrollbar styling for tooltip RTE */
									.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar {
										width: 6px !important;
									}
									.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-track {
										background: #f1f1f1 !important;
										border-radius: 3px !important;
									}
									.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb {
										background: #c1c1c1 !important;
										border-radius: 3px !important;
									}
									.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb:hover {
										background: #a8a8a8 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg * {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg div {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg span {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg br {
										color: #000000 !important;
									}
									/* Override any inherited styles from tooltip/modal */
									.jodit-container .jodit-wysiwyg {
										color: #000000 !important;
										background-color: #ffffff !important;
									}
									.jodit-container .jodit-wysiwyg * {
										color: #000000 !important;
									}
									/* Ensure text is visible in all states */
									.jodit-wysiwyg[contenteditable="true"] {
										color: #000000 !important;
										background-color: #ffffff !important;
										line-height: 1.4 !important;
										padding: 8px !important;
									}
									.jodit-wysiwyg[contenteditable="true"] * {
										color: #000000 !important;
									}
									.jodit-wysiwyg[contenteditable="true"] p {
										margin: 0 0 4px 0 !important;
										line-height: 1.4 !important;
									}
									/* Override any modal or tooltip text color inheritance */
									.MuiTooltip-tooltip .jodit-wysiwyg,
									.MuiTooltip-tooltip .jodit-wysiwyg * {
										color: #000000 !important;
									}
									/* Fix Jodit dialog positioning - target correct classes */
									.jodit.jodit-dialog {
										position: fixed !important;
										z-index: 100001 !important;
										top: 50% !important;
										left: 50% !important;
										transform: translate(-50%, -50%) !important;
									}
									.jodit-dialog .jodit-dialog__panel {
										position: relative !important;
										top: auto !important;
										left: auto !important;
										transform: none !important;
										max-width: 400px !important;
										background: white !important;
										border: 1px solid #ccc !important;
										border-radius: 4px !important;
										box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
									}
									/* Fix for link dialog specifically */
									.jodit-dialog_alert {
										position: fixed !important;
										z-index: 100001 !important;
										top: 50% !important;
										left: 50% !important;
										transform: translate(-50%, -50%) !important;
									}
									/* Style the inline toolbar */
									.jodit-toolbar-popup {
										background-color: white !important;
										border: 1px solid #ddd !important;
										border-radius: 4px !important;
										box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
										z-index: 100002 !important;
									}
									.jodit-toolbar-popup .jodit-toolbar__box {
										background-color: white !important;
										padding: 4px !important;
									}
									/* RTE container hover styles */
									.rte-container:hover .rte-action-icons {
										opacity: 1 !important;
									}
									.rte-action-icons {
										opacity: 0 !important;
										transition: opacity 0.2s ease-in-out !important;
									}
								`}
							</style>
							<JoditEditor
								value={rteBoxValue || ""}
								config={joditConfig}
								onChange={handleContentChange}
							/>
					</div>
				</Box>
			</>
		);
	}
);

export default memo(RTEsection);
