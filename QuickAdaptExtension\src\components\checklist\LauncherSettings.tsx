import React, { useReducer, useState, useEffect } from "react";
import { <PERSON>, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, ToggleButton, ToggleButtonGroup, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import {
	chkicn1,
	chkicn2,
	chkicn3,
	chkicn4,
	chkicn5,
	chkicn6,
	InfoFilled,
	QuestionFill,
	Reselect,
	editicon,
	Solid,
	launlftun,
	launrgtse,
	laun<PERSON>tse,
	launrgtun,
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import InsertPhotoIcon from "@mui/icons-material/InsertPhoto";
import PersonIcon from "@mui/icons-material/Person";
import FavoriteIcon from "@mui/icons-material/Favorite";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { position } from "jodit/esm/core/helpers";
import { useTranslation } from 'react-i18next';


const LauncherSettings = ({ currentGuide }: any) => {
	const { t: translate } = useTranslation();

	
	const {

		titlePopup,
		setTitlePopup,
		setDesignPopup,
		titleColor,
		setTitleColor,
		launcherColor,
		setLauncherColor,
		iconColor,
		setIconColor,
		setShowLauncherSettings,
		checklistGuideMetaData,
		updateChecklistLauncher,
		setIsUnSavedChanges,
	} = useDrawerStore((state: any) => state);

	const encodeToBase64 = (svgString: string) => {
		return `data:image/svg+xml;base64,${btoa(svgString)}`;
	  };
	  
	  const [icons, setIcons] = useState<any[]>(() => {
		return [
		  { id: 1, base64: encodeToBase64(chkicn1), component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		  { id: 2, base64: encodeToBase64(chkicn2), component: <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		  { id: 3, base64: encodeToBase64(chkicn3), component: <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		  { id: 4, base64: encodeToBase64(chkicn4), component: <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		  { id: 5, base64: encodeToBase64(chkicn5), component: <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		  { id: 6, base64: encodeToBase64(chkicn6), component: <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1, display: "flex" }} />, selected: false },
		];
	  });
	const [
		checklistLauncherProperties, setChecklistLauncherProperties] = useState<any>(() => {
		const initialchecklistLauncherProperties = checklistGuideMetaData[0]?.launcher || {
			type: "Icon",
			icon: "",
			text: "Get Started",
			iconColor: "#fff",
			textColor: "#fff",
			launcherColor: "var(--primarycolor)",
			launcherposition: {
				left: false,
				right: true,
				xaxisOffset: "10",
				yaxisOffset: "10",
			},
			notificationBadge: false,
			notificationBadgeColor: "red",
			notificationTextColor: "#fff",

		};
		return initialchecklistLauncherProperties;
	});

	// State for tracking changes and apply button
	const [isDisabled, setIsDisabled] = useState(true);
	const [hasChanges, setHasChanges] = useState(false);
	const [initialState, setInitialState] = useState(() => {
		// Use the actual data from store if available, otherwise use the default
		return checklistGuideMetaData[0]?.launcher || checklistLauncherProperties;
	});

	// Sync local state with store data only when component mounts (not on every store change)
	useEffect(() => {
		if (checklistGuideMetaData[0]?.launcher) {
			const newLauncher = checklistGuideMetaData[0].launcher;
			setChecklistLauncherProperties(newLauncher);
			setInitialState(newLauncher);
			setHasChanges(false);
			setIsDisabled(true);
			// Clear any validation errors on mount
			setTextError("");
		} else {
			// If no launcher data exists, ensure default icon is set in initialState
			const defaultIcon = icons[0];
			if (defaultIcon) {
				const svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;
				if (svgElement) {
					const base64Icon = encodeToBase64(svgElement);
					const defaultProperties = {
						...checklistLauncherProperties,
						icon: base64Icon
					};
					setInitialState(defaultProperties);
				}
			}
		}
	}, []); // Empty dependency array - only run on mount

	// State for tracking validation errors
	const [textError, setTextError] = useState("");
	// Function to check if the Apply button should be enabled
	const updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {
		setIsDisabled(!changed || hasErrors);
	};

	// Effect to check for any changes compared to initial state
	useEffect(() => {
		// Compare current properties with initial state
		const hasAnyChanges = JSON.stringify(checklistLauncherProperties) !== JSON.stringify(initialState);
		setHasChanges(hasAnyChanges);

		// Check for validation errors
		const hasValidationErrors = !!textError;

		updateApplyButtonState(hasAnyChanges, hasValidationErrors);
	}, [checklistLauncherProperties, initialState, textError]);
	const handleTitleColorChange = (e: any) => setTitleColor(e.target.value);

	useEffect(() => {
		if (checklistLauncherProperties.icon) {
		  setIcons((prevIcons) =>
			prevIcons.map((icon) => ({
			  ...icon,
			  selected: icon.base64 === checklistLauncherProperties.icon, // Compare Base64 strings directly
			}))
		  );
		}
	}, [checklistLauncherProperties.icon]);

	
	const handleClose = () => {
		setShowLauncherSettings(false);
	};
	const handledesignclose = () => {
		setDesignPopup(false);
	};
	const handleSizeChange = (value: number) => {
		const sizeInPx = 16 + (value - 1) * 4;
		onPropertyChange("Size", sizeInPx);
	};

	const onReselectElement = () => {

	};

	const onPropertyChange = (key: any, value: any) => {
		// Validate text input
		if (key === "text") {
			let errorMessage = "";
			if (value.length < 2) {
				errorMessage = translate("Min: 2 Characters");
			} else if (value.length > 20) {
				errorMessage = translate("Max: 20 Characters");
			}
			setTextError(errorMessage);
		}


		setChecklistLauncherProperties((prevState: any) => {
			let newState;
			// Handle nested launcherposition properties
			if (key === "xaxisOffset" || key === "yaxisOffset") {
				newState = {
					...prevState,
					launcherposition: {
						...prevState.launcherposition,
						[key]: value,
					},
				};
			} else {
				// Handle other properties normally
				newState = {
					...prevState,
					[key]: value,
				};
			}
			// Mark that changes have been made
			setHasChanges(true);
			return newState;
		});
	};

	const handleIconColorChange = (e: any) => {
		setIconColor(e.target.value);
	}
	const handleLauncherColorChange = (e: any) => { setLauncherColor(e.target.value) }

	const handleApplyChanges = () => {
		updateChecklistLauncher(checklistLauncherProperties);
		// Update the initial state to the current state after applying changes
		setInitialState({ ...checklistLauncherProperties });
		// Reset the changes flag
		setHasChanges(false);
		// Disable the Apply button
		setIsDisabled(true);
		setIsUnSavedChanges(true);
		handleClose();
	};
	const [type, setType] = useState('Text');
	const [text, setText] = useState('Get Started');
	const [textColor, setTextColor] = useState('#ffffff');

	const handleTypeChange = (newType: any) => {
		setType(newType);
		onPropertyChange("type", newType);
	
		// // Reset icon selection when type changes
		// setIcons(prevIcons => prevIcons.map(icon => ({ ...icon, selected: false })));
	
		// // Also reset the selected icon in checklistLauncherProperties
		// setChecklistLauncherProperties((prev:any) => ({
		// 	...prev,
		// 	icon: null, // Clear the selected icon
		// }));
	};
	
	const [error, setError] = useState<string | null>(null);
	
	const [icon, setIcon] = useState<any>();


		// Helper function to convert SVG to Base64
		const svgToBase64 = (svgString: string): string => {
			return `data:image/svg+xml;base64,${btoa(svgString)}`;
		};
	
useEffect(() => {
	const defaultIcon = icons[0];
	if (defaultIcon && !checklistLauncherProperties.icon) {
		const svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;
		if (svgElement) {
			const base64Icon = encodeToBase64(svgElement);
			const appliedIconColorBase64Icon = modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);
			setIcon(base64Icon);

			// Create the updated properties with default icon
			const updatedProperties = {
				...checklistLauncherProperties, 
				icon: base64Icon, 
			};

			// Update the state
			setChecklistLauncherProperties(updatedProperties);
			
			// IMPORTANT: Also update the initialState to include the default icon
			// This prevents the change detection from thinking there's a user change
			setInitialState(updatedProperties);
			
			// Update the store
			updateChecklistLauncher(updatedProperties);
		}
	}
}, [])

	const modifySVGColor = (base64SVG: any, color: any) => {
		if (!base64SVG) {
			return "";
		}

		try {
			// Check if the string is a valid base64 SVG
			if (!base64SVG.includes("data:image/svg+xml;base64,")) {
				return base64SVG; // Return the original if it's not an SVG
			}

			const decodedSVG = atob(base64SVG.split(",")[1]);

			// Check if this is primarily a stroke-based or fill-based icon
			const hasStroke = decodedSVG.includes('stroke="');
			const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

			let modifiedSVG = decodedSVG;

			if (hasStroke && !hasColoredFill) {
				// This is a stroke-based icon (like chkicn2-6) - only change stroke color
				modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
			} else if (hasColoredFill) {
				// This is a fill-based icon (like chkicn1) - only change fill color
				modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
			} else {
				// No existing fill or stroke, add fill to make it visible
				modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
				modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
			}

			const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
			return modifiedBase64;
		} catch (error) {
			// console.error("Error modifying SVG color:", error);
			return base64SVG; // Return the original if there's an error
		}
	};
		const handleIconClick = async (id: number) => {
			setIcons((prevIcons) =>
				prevIcons.map((icon) => ({
					...icon,
					selected: icon.id === id,
				}))
			);
		
			const selectedIcon = icons.find((icon) => icon.id === id);
			if (selectedIcon) {
				const svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;
				if (svgElement) {
					const base64Icon = svgToBase64(svgElement);
					const appliedIconColorBase64Icon=modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);
					setIcon(base64Icon);
		

					setChecklistLauncherProperties((prevState:any) => ({
						...prevState, // Copy previous state
						icon: base64Icon, // Update icon property
					}));
					setHasChanges(true);
				}
			}
		};
		
		const [setPositionLeft, setSetPositionLeft] = useState(false);
  const [launlft, setLaunlft] = useState(launlftun); 
  const [launrgt, setLaunrgt] = useState(launrgtse); 

  const handleLauncherLeft = () => {
	setChecklistLauncherProperties((prev:any) => ({
	  ...prev,
	  launcherposition: {
		...prev.launcherposition,
		left: true, 
		right: false, 
	  },
	}));
	setHasChanges(true);
	setLaunlft(launlftse); 
	setLaunrgt(launrgtun); 
	};
	useEffect(() => {
		const position = checklistLauncherProperties.launcherposition;
		if (position.left) {
		  setLaunlft(launlftse); 
		  setLaunrgt(launrgtun); 
		} else if (position.right) {
		  setLaunlft(launlftun); 
		  setLaunrgt(launrgtse); 
		}
	  }, [checklistLauncherProperties.launcherposition]); 
  

  const handleLauncherRight = () => {
	setChecklistLauncherProperties((prev:any) => ({
		...prev,
		launcherposition: {
		  ...prev.launcherposition, 
		  left: false, 
		  right: true, 
		},
	  }));
	setHasChanges(true);
	setLaunlft(launlftun);
    setLaunrgt(launrgtse); 
  };
	  


	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label={translate('back', { defaultValue: 'back' })}
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">{translate('Launcher', { defaultValue: 'Launcher' })}</div>
					<IconButton
						size="small"
						aria-label={translate('close', { defaultValue: 'close' })}
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div className="qadpt-canblock">
					<div className="qadpt-controls">

						<div className="qadpt-control-box"
																		style={{height: "auto", flexDirection:"column" ,padding:"0"}}>
							<div className="qadpt-control-label">{translate('Type', { defaultValue: 'Type' })}</div>
							<div style={{ display:"inline-block",gap:"5px",padding:"0 8px 8px 8px",textAlign:"left"}
							}>
								<button
									className={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon' ? 'selected' : ''}`}
									onClick={() => handleTypeChange('Icon')}
								>
									{translate('Icon', { defaultValue: 'Icon' })}
								</button>
								<button
									className={`qadpt-type-option ${checklistLauncherProperties.type === 'Text' ? 'selected' : ''}`}
									onClick={() => handleTypeChange('Text')}
								>
									{translate('Text', { defaultValue: 'Text' })}
								</button>
								<button
									className={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon+Txt' ? 'selected' : ''}`}
									onClick={() => handleTypeChange('Icon+Txt')}
								>
									{translate('Icon+Text', { defaultValue: 'Icon+Text' })}
								</button>
							</div>


						</div>

						{checklistLauncherProperties.type === "Text" && (
							<>
							
									<Box
																		id="qadpt-designpopup"
																		className="qadpt-control-box"
																		sx={{ flexDirection: "column", height: "auto !important" }}
																	>
									<div className="qadpt-control-label" >{translate('Text', { defaultValue: 'Text' })}</div>
									
																		<TextField
																				variant="outlined"
																				size="small"
										placeholder={translate('Step Title', { defaultValue: 'Step Title' })}
																				className="qadpt-control-input"
																				style={{width: "calc(100% - 13px)",padding: "0 8px 8px 8px", margin:"0"}}
																				value={checklistLauncherProperties.text}
																				onChange={(e) => onPropertyChange("text", e.target.value)}
																				error={Boolean(textError)}
																				helperText={textError}
																				InputProps={{
																					endAdornment: "",
																					sx: {
																	 
																						"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
																						"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
																						"& fieldset":{border:"none"},
																						"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
																						"&.MuiInputBase-root":{height:"auto !important"}
																					},
																				}}
																			/>
																</Box>
								
								<Box className="qadpt-control-box">
									<Typography className="qadpt-control-label">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>
							<input
								type="color"
								value={checklistLauncherProperties?.textColor}
								onChange={(e) => onPropertyChange("textColor", e.target.value)}
								className="qadpt-color-input"
							/>
						</Box>
								</>
						)}


						{checklistLauncherProperties.type === "Icon" && (
							<>
									
													<Box id="qadpt-designpopup" className="qadpt-control-box" sx={{ flexDirection: "column", height: "auto !important",padding:"0 !important" }}>
									<Typography className="qadpt-control-label">{translate('Icon', { defaultValue: 'Icon' })}</Typography>
										<Box sx={{ display: "flex", gap: 1, alignItems: "center", width: "-webkit-fill-available", flexWrap: "wrap", padding: "0 8px 8px 8px" }}>
										{icons.map(icon => (
											<Tooltip arrow key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>
										<IconButton
											onClick={() => handleIconClick(icon.id)}
											sx={{
												border: icon.selected ? "2px solid var(--primarycolor)" : "none",
																borderRadius: "8px",
																padding: "8px",
																background:"#F1ECEC",
											}}
										>
											{icon.component}
										</IconButton>
									</Tooltip>
								))}
									</Box>
								</Box>
										

									<Box className="qadpt-control-box">
									<Typography className="qadpt-control-label">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>
							<input
								type="color"
								value={checklistLauncherProperties?.iconColor}
								onChange={(e) => onPropertyChange("iconColor", e.target.value)}
										className="qadpt-color-input"
										style={{backgroundColor:'#fff'}}

							/>
						</Box>


								
								</>

						)
						}

						{checklistLauncherProperties.type === "Icon+Txt" && (
							<>
								
								<Box
																		id="qadpt-designpopup"
																		className="qadpt-control-box"
																		sx={{ flexDirection: "column", height: "auto !important" }}
																	>
									<div className="qadpt-control-label" >{translate('Text', { defaultValue: 'Text' })}</div>
									
																		<TextField
																				variant="outlined"
																				size="small"
										placeholder={translate('Step Title', { defaultValue: 'Step Title' })}
																				className="qadpt-control-input"
																				style={{width: "calc(100% - 13px)",padding: "0 8px 8px 8px", margin:"0"}}
																				value={checklistLauncherProperties.text}
																				onChange={(e) => onPropertyChange("text", e.target.value)}
																				error={Boolean(textError)}
																				helperText={textError}
																				InputProps={{
																					endAdornment: "",
																					sx: {
																	 
																						"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
																						"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
																						"& fieldset":{border:"none"},
																						"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
																						"&.MuiInputBase-root":{height:"auto !important"}
																					},
																				}}
																			/>
																</Box>
								
								<Box id="qadpt-designpopup" className="qadpt-control-box" sx={{ flexDirection: "column", height: "auto !important" }}>
									<div className="qadpt-control-label" >{translate('Icon', { defaultValue: 'Icon' })}</div>
										<Box sx={{ display: "flex", gap: 1, alignItems: "center", width: "-webkit-fill-available", flexWrap: "wrap", padding: "0 8px 8px 8px" }}>
										{icons.map(icon => (
											<Tooltip arrow key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>
										<IconButton
											onClick={() => handleIconClick(icon.id)}
											sx={{
												border: icon.selected ? "2px solid var(--primarycolor)" : "none",
																borderRadius: "8px",
																padding: "8px",
																background:"#F1ECEC",
											}}
										>
												{icon.component}
											</IconButton>
										</Tooltip>
									))}

									{/* Upload New Icon */}
									{/* <input
										type="file"
										accept=".ico"
										id="icon-upload"
										style={{ display: "none" }}
										onChange={handleFileUpload}
									/> */}
								</Box>


								</Box>
								

								<Box className="qadpt-control-box">
									<Typography className="qadpt-control-label">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>
							<input
								type="color"
								value={checklistLauncherProperties?.iconColor}
								onChange={(e) => onPropertyChange("iconColor", e.target.value)}
										className="qadpt-color-input"
										style={{backgroundColor:'#fff'}}

							/>
								</Box>
								<Box className="qadpt-control-box">
									<Typography className="qadpt-control-label">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>
							<input
								type="color"
								value={checklistLauncherProperties?.textColor}
								onChange={(e) => onPropertyChange("textColor", e.target.value)}
								className="qadpt-color-input"
							/>
						</Box>
							</>
						)}

					


						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate('Launcher Color', { defaultValue: 'Launcher Color' })}</div>
							<div>
							<input
								type="color"
								value={checklistLauncherProperties?.launcherColor}
								onChange={(e) => onPropertyChange("launcherColor", e.target.value)}
									className="qadpt-color-input"
									style={{backgroundColor:'#5F9EA0'}}

								/>
								</div>
						</Box>



						<Box className="qadpt-control-box" sx={{height:"auto !important",flexDirection:"column !important",padding:"0 !important"}}>
							<div className="qadpt-control-label">{translate('Position', { defaultValue: 'Position' })}</div>
							<div style={{    padding: "0 8px 8px 8px",display: "flex",gap: "4px",cursor:"pointer"}}>
								
								<span dangerouslySetInnerHTML={{ __html: launlft }} onClick={handleLauncherLeft} style={{ zoom: "0.95" }} />
								
								<span dangerouslySetInnerHTML={{ __html:  launrgt}} onClick={handleLauncherRight} style={{ zoom: "0.95" }} />
							</div>
							<div style={{display : "flex", alignItems : "center",justifyContent : "space-between",paddingRight : "8px"}}>
								<span className="qadpt-control-label" style={{whiteSpace: "normal",
									wordBreak: "break-word"
								}}>X {translate('Axis Offset', { defaultValue: 'X Axis Offset' })}</span>
							<span className="qadpt-chkoffset">
							<TextField
								variant="outlined"
								value={checklistLauncherProperties.launcherposition?.xaxisOffset || "10"}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => onPropertyChange("xaxisOffset", e.target.value)}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset": { border: "none" },

									},
								}}

								/>
								</span>
						</div>
						<div style={{display : "flex", alignItems : "center",justifyContent : "space-between",paddingRight : "8px"}}>
								<div className="qadpt-control-label" style={{whiteSpace: "normal",
									wordBreak: "break-word"
								}}>Y {translate('Axis Offset', { defaultValue: 'Y Axis Offset' })}</div>
							<div className="qadpt-chkoffset">
							<TextField
								variant="outlined"
								value={checklistLauncherProperties.launcherposition?.yaxisOffset || "10"}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => onPropertyChange("yaxisOffset", e.target.value)}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset": { border: "none" },

									},
								}}

								/>
								</div>
						</div>
								</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate('Notification Badge', { defaultValue: 'Notification Badge' })}</div>

							{/* Show by Default Toggle */}
<div>
							<label className="toggle-switch">
								<input
									type="checkbox"
									checked={checklistLauncherProperties.notificationBadge}
									onChange={(e) => onPropertyChange("notificationBadge", e.target.checked)}

									name="showByDefault"
								/>
								<span className="slider"></span>
							</label>
							</div>
						</Box>

						{checklistLauncherProperties.notificationBadge && (
							<>
								<Box className="qadpt-control-box">
									<div className="qadpt-control-label">{translate('Badge Color', { defaultValue: 'Badge Color' })}</div>
									<div>
									<input
										type="color"
										value={checklistLauncherProperties?.notificationBadgeColor}
										onChange={(e) => onPropertyChange("notificationBadgeColor", e.target.value)}
											className="qadpt-color-input"
											style={{backgroundColor:'red'}}

										/>
										</div>
								</Box>
								<Box className="qadpt-control-box">
									<div className="qadpt-control-label">{translate('Text Color', { defaultValue: 'Text Color' })}</div>
									<div>
									<input
										type="color"
										value={checklistLauncherProperties?.notificationTextColor}
										onChange={(e) => onPropertyChange("notificationTextColor", e.target.value)}
										className="qadpt-color-input"
										/>
										</div>
								</Box>
							</>
						)}


					</div>




				</div>
				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn ${isDisabled ? "disabled" : ""}`}
						disabled={isDisabled}
					>
						{translate('Apply', { defaultValue: 'Apply' })}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default LauncherSettings;
