/* Popup loader positioned in top-right corner of the webpage */
.qadpt-popup-loader {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999999 !important; /* Higher than other extension elements */
  font-family: var(--qadptfont-family, Poppins, Proxima Nova, arial, serif) !important;
  pointer-events: none; /* Don't interfere with page interactions */
}

.qadpt-loader-container {
  background-color: var(--white-color, #fff);
  border: 1px solid var(--border-color, #ccc);
  border-radius: var(--button-border-radius, 12px);
  padding: 16px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 140px;
  animation: qadpt-loader-fadeIn 0.3s ease-out;
}

.qadpt-loader-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color, #ccc);
  border-top: 2px solid var(--primarycolor, #5F9EA0);
  border-radius: 50%;
  animation: qadpt-loader-spin 1s linear infinite;
  flex-shrink: 0;
}

.qadpt-loader-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--primarycolor, #5F9EA0);
  white-space: nowrap;
}

/* Animations */
@keyframes qadpt-loader-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

@keyframes qadpt-loader-fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes qadpt-loader-fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

/* Fade out animation class */
.qadpt-popup-loader.fade-out .qadpt-loader-container {
  animation: qadpt-loader-fadeOut 0.3s ease-in forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .qadpt-popup-loader {
    top: 10px;
    right: 10px;
  }
  
  .qadpt-loader-container {
    padding: 12px 16px;
    min-width: 120px;
  }
  
  .qadpt-loader-spinner {
    width: 18px;
    height: 18px;
  }
  
  .qadpt-loader-text {
    font-size: 13px;
  }
}

/* RTL support */
.rtl .qadpt-popup-loader {
  right: auto;
  left: 20px;
}

@media (max-width: 768px) {
  .rtl .qadpt-popup-loader {
    left: 10px;
  }
}
