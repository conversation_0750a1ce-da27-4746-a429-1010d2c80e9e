import React, { useEffect, useState } from "react";
import { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import Modal from '@mui/material/Modal';
import { useTranslation } from 'react-i18next';

import DriveFolderUploadIcon from '@mui/icons-material/DriveFolderUpload';
import BackupIcon from '@mui/icons-material/Backup';
import { getAllFiles } from "../../services/FileService";
import { FileUpload } from "../../models/FileUpload";



const SelectImageFromApplication = ({ isOpen, handleModelClose, onImageSelect, handleImageUpload, setFormOfUpload, formOfUpload,handleReplaceImage, isReplaceImage, onImageUrlSelect, showUrlInput = true, showFileList = true }: any) => {
	const { t: translate } = useTranslation();

	const [files, setFiles] = useState<FileUpload[]>([]);
 	const [imageUrl, setImageUrl] = useState("");

	const getAllFilesData = async () => {
		try {
			const data = await getAllFiles();
			if (data) {
				const uploads: any = data.map((file:any) => ({
					ImageId: file.Id, 
					FileName: file.Name || null,
					Url: file.Url || '',
				}));
				setFiles(uploads);
			}else{
			}
		} catch (error) {
		}
	}

	useEffect(() => {
		getAllFilesData();
	}, []);

	return (<>
		<Modal open={isOpen} onClose={handleModelClose} sx={{zIndex:"999999 !important"}}>
			<Box sx={{
					position: "absolute",
					top: "50%",
					left: "50%",
					transform: "translate(-50%, -50%)",
					width: 450,
					bgcolor: "background.paper",
					boxShadow: 24,
					p: 4,
					maxHeight: "400px",
				overflow: "auto",
					zIndex: "9999"
				}}>
 				{showUrlInput && (
 				<Box mb={2}>
 					<Typography variant="subtitle1">{translate("Enter Image URL")}</Typography>
 					<Box display="flex" gap={1} mt={1}>
 						<TextField
 							fullWidth
 							variant="outlined"
 							size="small"
 							value={imageUrl}
 							onChange={e => setImageUrl(e.target.value)}
 							placeholder={translate("Paste image link here")}
 						/>
 						<Button
 							variant="contained"
 							disabled={!imageUrl}
 							onClick={() => {
 								if (onImageUrlSelect) {
 									onImageUrlSelect(imageUrl);
 									setImageUrl("");
 								}
 							}}
 						>
 							{translate("Confirm")}
 						</Button>
 					</Box>
 				</Box>
 				)}
				{showFileList && (
				<Box>
					<Typography variant="h6">{translate("Select a File")}</Typography>
					<Box sx={{ display: "flex", flexWrap: "wrap" }}>
						{
							files && (
								files.map((file) => {
									return (
										<Box sx={{ width: "100px", height: "100px", mx: 2 }} >
											<img src={file?.Url} style={{ width: "inherit" }} onClick={() => { onImageSelect(file) }} alt={translate("Uploaded file image")} />
										</Box>
									);
								})
							)
						}
					</Box>
				</Box>
				)}
			</Box>
		</Modal>
	</>);
}


export default SelectImageFromApplication;