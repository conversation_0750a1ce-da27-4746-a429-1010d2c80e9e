import React, { useState, useEffect, useContext } from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	TextField,
	InputAdornment,
	IconButton,
	Tab,
	Tabs,
	Tooltip,
	DialogTitle,
	DialogContentText,
	DialogActions,
	Button,
	Typography,
} from "@mui/material";
import { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from "@mui/x-data-grid";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";

import { getAllGuides, DeleteGuideByGuideId } from "../../../services/GuideListServices";
import { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from "../../../assets/icons/icons";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import "./GuideMenuOptions.css";
import AddIcon from "@mui/icons-material/Add";
import CloneInteractionDialog from "./CloneGuidePopUp";
import { AccountContext } from "../../login/AccountContext";
import { useSnackbar } from "./SnackbarContext";
import CloseIcon from "@mui/icons-material/Close";
import { formatDateTime } from "../../guideSetting/guideList/TimeZoneConversion";
import useDrawerStore from "../../../store/drawerStore";
import useUserSession from "../../../store/userSession";
import { useTranslation } from 'react-i18next';
import { useAuth } from "../../auth/AuthProvider";
import useInfoStore from "../../../store/UserInfoStore";

let editedguide: any;
interface PopupModalProps {
	title: string;
	Open: boolean;
	onClose: () => void;
	searchText: string;
	onAddClick: (searchText: string, isEditing?: boolean, guideDetails?: any) => void;
}
interface Announcement {
	AccountId: string;
	Content: string;
	CreatedBy: string;
	CreatedDate: string;
	Frequency: string;
	GuideId: string;
	GuideStatus: string;
	GuideType: string;
	Name: string;
	OrganizationId: string;
	Segment: string;
	TargetUrl: string;
	TemplateId: string;
	UpdatedBy: string;
	UpdatedDate: string;
}

const PopupModal: React.FC<PopupModalProps> = ({ Open, onClose, title, searchText, onAddClick }) => {
	const { setCurrentGuideId, currentGuideId } = useUserSession((state) => state);
	const { t: translate } = useTranslation();
	const {
		setBannerPopup,
		setOpenTooltip,
		setElementSelected,
		setBannerButtonSelected,
		selectedTemplateTour,
		isUnSavedChanges,
		setIsUnSavedChanges,
		openWarning,
		setOpenWarning,
		setEditClicked,
		setActiveMenu,
		setSearchText,
	} = useDrawerStore((state) => state);
	const [activeTab, setActiveTab] = useState(0);
	const [searchQuery, setSearchQuery] = useState("");
	const [filteredData, setFilteredData] = useState<any[]>([]);
	const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);
	const [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);
	const [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);
	const [GuidenametoDelete, setGuideNametoDelete] = useState("");
	const [GuideTypetoDelete, setGuideTypetoDelete] = useState("");
	const [openDialog, setOpenDialog] = useState(false);
	const userType = useInfoStore((state) => state.userType); 
	const [paginationModel, setPaginationModel] = useState({
		page: 0,
		pageSize: 15,
	});
	const { accountId,roles } = useContext(AccountContext);
	const { openSnackbar } = useSnackbar();
	const [totalCount, setTotalCount] = useState(0);
	const [name, setName] = useState("Announcement");
	const handleEditClick = (guide: Announcement) => {
		setBannerButtonSelected(true);
		setIsUnSavedChanges(false);
		setEditClicked(true);
		setOpenWarning(false);
		let targetUrl = "";
		editedguide = true;
		if (
			guide.GuideType.toLowerCase() == "announcement" ||
			guide.GuideType.toLowerCase() === "tooltip" ||
			guide.GuideType.toLowerCase() === "hotspot" ||
			guide.GuideType.toLowerCase() === "tour" ||
			guide.GuideType.toLowerCase() === "checklist"
		) {
			if (
				guide.GuideType.toLowerCase() === "tooltip" ||
				guide.GuideType.toLowerCase() === "hotspot" ||
				guide.GuideType.toLowerCase() === "banner" ||
				selectedTemplateTour === "Tooltip" ||
				selectedTemplateTour === "Banner" ||
				selectedTemplateTour === "Hotspot"
			) {
				setOpenTooltip(true);
				setElementSelected(true);
				let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
				const bodyElement = document.body;

				// Add a dynamic class to the body
				bodyElement.classList.add("dynamic-body-style");

				if (!styleTag) {
					styleTag = document.createElement("style");
					styleTag.id = "dynamic-body-style";

					// Add styles for body and nested elements
					let styles = `
						.dynamic-body-style {
							padding-top: 50px !important;
							max-height:calc(100% - 55px);
						}
						
					`;

					styleTag.innerHTML = styles;
					document.head.appendChild(styleTag);
				}
			}
			targetUrl = `${guide?.TargetUrl}`;
			if (targetUrl !== window.location.href) {
				setCurrentGuideId(guide.GuideId);
				window.open(targetUrl);
			} else {
				setCurrentGuideId(guide.GuideId);
			}

			return;
		} else if (guide.GuideType.toLowerCase() == "banner" || selectedTemplateTour === "Banner") {
			//targetUrl = `${guide?.TargetUrl}#bannerEdit`;
			setCurrentGuideId(guide.GuideId);
			setBannerPopup(true);
		}
		if (targetUrl) {
			//onAddClick(guide.GuideType, true, guide);
			window.open(targetUrl);
		}
	};
	
	const handleCopyClick = (announcement: Announcement) => {
		setCloneAnnouncementName(announcement);
		setIsCloneDialogOpen(true);
	};
	const handleDeleteConfirmation = (guideId: string) => {
		setGuideIdToDelete(guideId);
		setOpenDialog(true);
	};
	const handleKeyDown = (event: React.KeyboardEvent) => {
		if (event.key === "Enter") {
			handleSearch();
		}
	};
	const columns: GridColDef[] = [
		{
			field: "Name",
			headerName: translate("Name"),
			// width: 300,
			hideable: true,
			resizable: false,
		},
		{
			field: "UpdatedDate",
			headerName: translate("Last Edited"),
			// width: 250,
			hideable: true,
			renderCell: (params: GridRenderCellParams) => (
				<span> {`${formatDateTime(params.row.UpdatedDate, "dd-MM-yyyy")}`}</span>
			),
			resizable: false,
		},
		{
			field: "actions",
			headerName: translate("Actions"),
			// width: 302,
			hideable: true,
			renderCell: (params: GridRenderCellParams) => (
				<>
					{ roles != null && roles && ["Account Admin","Editor"].some(role => roles.includes(role)) &&<>
						<Tooltip
							arrow
							title={translate("Edit")}
						>
							<IconButton onClick={() => handleEditClick(params.row)}>
								<span
									dangerouslySetInnerHTML={{ __html: ListEditIcon }}
									style={{ zoom: 0.7 }}
								/>
							</IconButton>
						</Tooltip>
					
					<Tooltip
						arrow
						title={translate("Clone")}
					>
						<IconButton onClick={() => handleCopyClick(params.row)}>
							<span
								dangerouslySetInnerHTML={{ __html: CopyListIcon }}
								style={{ zoom: 0.7 }}
							/>
						</IconButton>
					</Tooltip>
					<Tooltip
						arrow
						title={translate("Delete")}
					>
						<IconButton
							onClick={() => {
								handleDeleteConfirmation(params.row.GuideId);
								setGuideNametoDelete(params.row.Name);
								setGuideTypetoDelete(params.row.GuideType);
							}}
						>
							<span
								dangerouslySetInnerHTML={{ __html: DeleteIconList }}
								style={{ zoom: 0.7 }}
							/>
						</IconButton>
						</Tooltip>
						</>
					}
				</>
			),
			resizable: false,
		},
	];

	const fetchAnnouncements = async () => {
		const { page, pageSize } = paginationModel;
		const offset = page * pageSize;
		const statusFilter = activeTab === 0 ? "Active" : activeTab === 1 ? "InActive" : "Draft";

		const filters = [
			{
				FieldName: "GuideType",
				ElementType: "string",
				Condition: "equals",
				Value: title === "Product Tours" ? "Tour" : title,
				IsCustomField: false,
			},
			{
				FieldName: "GuideStatus",
				ElementType: "string",
				Condition: "equals",
				Value: statusFilter,
				IsCustomField: false,
			},
			{
				FieldName: "Name",
				ElementType: "string",
				Condition: "contains",
				Value: searchQuery,
				IsCustomField: false,
			},
			{
				FieldName: "AccountId",
				ElementType: "string",
				Condition: "contains",
				Value: accountId,
				IsCustomField: false,
			},
		];
		const data = await getAllGuides(offset, pageSize, filters, "");
		const rowsWithIds = data?.results?.map((item: any) => ({
			...item,
			id: item.GuideId,
		}));

		setFilteredData(rowsWithIds || []);
		setTotalCount(data?._count);
	};

	useEffect(() => {
		if (Open || accountId) {
			fetchAnnouncements();
		}
	}, [paginationModel, activeTab, Open, accountId]);

	// useEffect(() => {
	//     if (accountId) {
	//       fetchAnnouncements();
	//     }
	//   }, [paginationModel, activeTab,accountId]);

	const handleSearch = () => {
		fetchAnnouncements();
	};

	useEffect(() => {
		if (searchQuery.trim() === "") {
			fetchAnnouncements();
		}
	}, [searchQuery]);
	const handleClearSearch = () => {
		setSearchQuery("");
		fetchAnnouncements();
	};

	const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
		setActiveTab(newValue);
		setPaginationModel((prev) => ({ ...prev, page: 0 })); // Reset pagination when the tab changes
	};
	const getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {
		return {
			top: params.isFirstVisible ? 0 : 5,
			bottom: params.isLastVisible ? 0 : 5,
		};
	}, []);

	const handleDelete = async () => {
		if (guideIdToDelete) {
			try {
				const response = await DeleteGuideByGuideId(guideIdToDelete);
				if (response.Success) {
					openSnackbar(
						`${GuidenametoDelete} ${translate(GuideTypetoDelete)} ${translate("Deleted Successfully")}`,
						"success"
					);
					await fetchAnnouncements();
				} else {
					openSnackbar(response.ErrorMessage, "error");
				}
			} catch (error) {}
		}
		setOpenDialog(false);
		setGuideIdToDelete(null);
		setGuideNametoDelete("");
	};
	const handleCloneSuccess = async () => {
		await fetchAnnouncements();
	};
	const getNoRowsLabel = () => {
		const tabLabels = [translate("Active"), translate("Inactive"), translate("Draft")];
		const currentTabLabel = tabLabels[activeTab] || searchText;
		return `${translate('No')} ${translate(currentTabLabel, { defaultValue: currentTabLabel })} ${translate(searchText, { defaultValue: `${searchText}s` })}`;
	};
	const NoRowsOverlay = () => (
		<div style={{ display: "flex", alignItems: "center", flexDirection: "column" }}>
			<span
				className="qadpt-hotsicon"
				dangerouslySetInnerHTML={{ __html: NoData }}
			/>
			<Typography sx={{ fontWeight: "600" }}>{getNoRowsLabel()}</Typography>
		</div>
	);

	const handleClosePopup = () => {
		setActiveMenu(null);
		setSearchText("");
		onClose();
	};

	return (
		<div id="popuplistmenu">
			<Dialog
				slotProps={{
					root: {
						id: "tooltipdialog",
					},
					backdrop: {
						sx: {
							position: "absolute !important",
						},
					},
				}}
				open={Open}
				onClose={handleClosePopup}
				fullWidth
				maxWidth="md"
				className="qadpt-gud-menupopup"
			>
				<DialogContent className="qadpt-gud-menupopup-content">
					<div
						className="qadpt-subhead"
						id="tablesubhead"
					>
						<span
	className="title"
	style={{ fontWeight: "600 !important" }}
>
							{translate(`${searchText}`, { defaultValue: `${translate(searchText)}s` })}
</span>
						{/* <IconButton
							sx={{
								borderRadius: "4px",
								borderWidth: "1px",
								borderStyle: "solid",
								padding: "5px",
								borderColor: "var(--border-color)",
							}}
							onClick={onClose}
						>
							<CloseIcon style={{ color: "#000" }} />
						</IconButton> */}
					</div>
					<div
						className="qadpt-head"
						id="table-head"
					>
						<div className="qadpt-titsection">
							<TextField
								variant="outlined"
								placeholder={translate("Search") + " " + translate(title)}
								value={searchQuery}
								onChange={(e) => {
									const newValue = e.target.value;
									setSearchQuery(newValue);
									if (newValue === "") {
										handleClearSearch();
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										handleSearch();
									}
								}}
								className="qadpt-extsearch"
								InputProps={{
									sx: {
										"&:hover .MuiOutlinedInput-notchedOutline": { borderColor: "#a8a8a8" }, // Prevents color change on hover
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "1px solid #a8a8a8" },
									},
									startAdornment: (
										<InputAdornment position="start">
											<IconButton
												aria-label="search"
												onClick={() => handleSearch()}
												onMouseDown={(event) => event.preventDefault()}
											>
												<SearchIcon />
											</IconButton>
										</InputAdornment>
									),
									endAdornment: searchQuery && (
										<InputAdornment position="end">
											<IconButton
												aria-label="clear"
												onClick={() => {
													setSearchQuery("");
													handleClearSearch();
												}}
											>
												<ClearIcon sx={{ zoom: "1.2" }} />
											</IconButton>
										</InputAdornment>
									),
								}}
							/>
							<div className="qadpt-right-part">
								<button
									onClick={() => onAddClick(searchText)}
									className="qadpt-memberButton"
									disabled={userType.toLocaleLowerCase()!="admin" ? roles==null || !roles || !["Account Admin", "Editor"].some(role => roles.includes(role)): false}
								>
									<AddIcon />
									<span>{`${translate("Create")} ${translate(searchText)}`}</span>
								</button>
							</div>
						</div>
					</div>

					<div className="qadpt-tabs-container">
						<Tabs
							value={activeTab}
							onChange={handleTabChange}
						>
							<Tab
								label={translate("Active")}
								sx={{
									backgroundColor: "inherit !important",
									border: "inherit !important",
									color: "inherit !important",
									fontSize: "14px !important",
								}}
							/>
							<Tab
								label={translate("Inactive")}
								sx={{
									backgroundColor: "inherit !important",
									border: "inherit !important",
									color: "inherit !important",
									fontSize: "14px !important",
								}}
							/>
							<Tab
								label={translate("Draft")}
								sx={{
									backgroundColor: "inherit !important",
									border: "inherit !important",
									color: "inherit !important",
									fontSize: "14px !important",
								}}
							/>
						</Tabs>
					</div>

					<div className="qadpt-webgird">
						<DataGrid
							rows={filteredData}
							columns={columns}
							getRowId={(row) => row.GuideId}
							getRowSpacing={getRowSpacing}
							pagination
							paginationModel={paginationModel}
							paginationMode="server"
							onPaginationModelChange={setPaginationModel}
							rowCount={totalCount}
							pageSizeOptions={[15, 25, 50, 100]}
							localeText={{
								MuiTablePagination: {
									labelRowsPerPage: translate("Records Per Page"),
								},
								noRowsLabel: getNoRowsLabel(),
							}}
							disableColumnMenu
							disableRowSelectionOnClick
							className="qadpt-grdcont"
							slots={{
								noRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay
							}}
							sx={{
								"& .MuiDataGrid-row": {
									maxWidth: "calc(100% - 30px)",
									"--rowBorderColor": "transparent",
									//   marginTop: "17px",
									// marginBottom:"0 !important"
								},
								"& .MuiDataGrid-cell": {
									padding: "0 15px !important",
								},
								".MuiTablePagination-toolbar": {
									display: "flex !important",
									alignItems: "baseline !important",
								},
								".MuiTablePagination-actions button": {
									border: "none !important",
									color: "inherit !important",
									backgroundColor: "initial !important",
									"&:hover": {
										backgroundColor: "initial !important", // Hover background
									},
								},
								"& .MuiDataGrid-columnHeader": {
									background: "linear-gradient(to right, #f6eeee, #f6eeee)",
									padding: "0 15px !important",
									borderRight: "1px solid #f6eeee",
									height: "40px !important",
								},
								"& .MuiDataGrid-columnHeaderTitle": {
									fontWeight: "600",
								},
								"& .MuiDataGrid-filler": {
									backgroundColor: "var(--ext-background)",
									"--rowBorderColor": "transparent !important",
								},
								"& .MuiDataGrid-scrollbarFiller": {
									backgroundColor: "var(--ext-background)",
									display: "none",
								},
							}}
							rowHeight={38}
						/>
					</div>
				</DialogContent>
			</Dialog>
			<Dialog
				open={openDialog}
				onClose={() => setOpenDialog(false)}
				PaperProps={{
					style: {
						borderRadius: "4px",
						maxWidth: "400px",
						textAlign: "center",
						maxHeight: "300px",
						boxShadow: "none",
					},
				}}
			>
				<DialogTitle sx={{ padding: 0 }}>
					<div style={{ display: "flex", justifyContent: "center", padding: "10px" }}>
						<div
							style={{
								backgroundColor: "#e4b6b0",
								borderRadius: "50%",
								width: "40px",
								height: "40px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							<DeleteOutlineOutlinedIcon sx={{ color: "#F44336", height: "20px", width: "20px" }} />
						</div>
					</div>
					<Typography sx={{ fontSize: "16px !important", fontWeight: 600, padding: "0 10px" }}>
						{translate("Delete")} {translate(GuideTypetoDelete)}
					</Typography>
				</DialogTitle>

				<DialogContent sx={{ padding: "20px !important" }}>
					<DialogContentText style={{ fontSize: "14px", color: "#000" }}>
						{translate(`${translate('The')} ${GuidenametoDelete} ${translate("cannot be restored once it is deleted.")}`)}
					</DialogContentText>
				</DialogContent>

				<DialogActions sx={{ justifyContent: "space-between", borderTop: "1px solid var(--border-color)" }}>
					<Button
						onClick={() => setOpenDialog(false)}
						sx={{
							color: "#9E9E9E",
							border: "1px solid #9E9E9E",
							borderRadius: "4px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
							lineHeight: "var(--button-lineheight)",
						}}
					>
						{translate("Cancel")}
					</Button>
					<Button
						onClick={handleDelete}
						sx={{
							backgroundColor: "var(--error-color)",
							color: "#FFF",
							borderRadius: "4px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
							lineHeight: "var(--button-lineheight)",
							// "&:hover": {
							// 	backgroundColor: "#D32F2F",
							// },
						}}
					>
						{translate("Delete")}
					</Button>
				</DialogActions>
			</Dialog>
			{isCloneDialogOpen && cloneAnnouncementName && (
				<CloneInteractionDialog
					open={isCloneDialogOpen}
					handleClose={() => setIsCloneDialogOpen(false)}
					initialName={cloneAnnouncementName}
					onCloneSuccess={handleCloneSuccess}
					name={name}
				/>
			)}
		</div>
	);
};
export { editedguide };
export default PopupModal;
