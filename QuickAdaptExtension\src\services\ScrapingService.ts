// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality

import i18n from '../multilinguial/i18n';

// Get the translation function directly from i18next
const t = i18n.t.bind(i18n);

/**
 * Translation keys used in this service:
 * 
 * "Click Scraping Active" - Main title for scraping instructions
 * "Click any element to scrape its XPath data" - Click element instruction
 * "Only the clicked element is scraped no duplicates" - Only clicked element instruction  
 * "Original click functionality still works" - Original click functionality instruction
 * "Red borders show scraped elements" - Red borders instruction
 * "Data is saved to Chrome storage" - Data saved instruction
 * "Scraped" - Feedback when element is scraped
 * "Element already scraped click blocked" - Tooltip for already scraped elements
 * 
 * This service uses the i18n instance directly for translations.
 * The translations will automatically fallback to the English keys if translation is missing.
 */

// Global state to track if scraping is active
let _isScrapingActive = false;
let _scrapedData: any[] = [];
let _lastScrapedTimestamp: string = '';
let _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath
let _clickListener: ((event: MouseEvent) => void) | null = null;
let _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements
let _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking
// Interface for element data
export interface ElementData {
  tagName: string;
  id: string;
  className: string;
  text: string;
  attributes: Record<string, string>;
  xpath: string;
  cssSelector: string;
  rect: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  children: ElementData[];
  isVisible: boolean;
  timestamp: string;
  url: string; // Add URL field to track which page the element is from
}

// Interface for scraped page data
export interface ScrapedPageData {
  url: string;
  title: string;
  timestamp: string;
  elements: ElementData[];
}

/**
 * Check if scraping is currently active
 */
export const isScrapingActive = (): boolean => {
  return _isScrapingActive;
};

/**
 * Set the scraping active state
 */
export const setScrapingActive = (active: boolean): void => {
  _isScrapingActive = active;
};

/**
 * Generate XPath for an element
 */
const generateXPath = (el: HTMLElement): string => {
  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';
  const path = [];
  while (el && el.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = el.previousElementSibling;
    while (sibling) {
      if (sibling.tagName === el.tagName) index++;
      sibling = sibling.previousElementSibling;
    }
    path.unshift(`${el.tagName}[${index}]`);
    el = el.parentElement!;
  }
  return '/' + path.join('/');
};

/**
 * Generate CSS selector for an element
 */
const generateCssSelector = (el: HTMLElement): string => {
  if (!el) return '';
  const path = [];
  while (el && el.nodeType === Node.ELEMENT_NODE) {
    let selector = el.tagName.toLowerCase();
    if (el.id) {
      selector += `#${el.id}`;
      path.unshift(selector);
      break;
    } else {
      // Safely handle className - it might be a string or SVGAnimatedString
      const className = getElementClassName(el);
      if (className) {
        selector += '.' + className.trim().replace(/\s+/g, '.');
      }
      path.unshift(selector);
      el = el.parentElement!;
    }
  }
  return path.join(' > ');
};

/**
 * Safely get className from an element (handles both HTML and SVG elements)
 */
const getElementClassName = (el: Element): string => {
  if (!el) return '';

  // For HTML elements, className is usually a string
  if (typeof el.className === 'string') {
    return el.className;
  }

  // For SVG elements, className might be an SVGAnimatedString
  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {
    return (el.className as any).baseVal || '';
  }

  // Fallback: try to get class attribute directly
  return el.getAttribute('class') || '';
};

/**
 * Check if element should be ignored for highlighting
 */
const shouldIgnoreHighlight = (element: HTMLElement): boolean => {
  return (
    element.classList.contains("mdc-tooltip__surface") ||
    element.classList.contains("mdc-tooltip__surface-animation") ||
    element.classList.contains("mdc-tooltip") ||
    element.classList.contains("mdc-tooltip--shown") ||
    element.classList.contains("mdc-tooltip--showing") ||
    element.classList.contains("mdc-tooltip--hiding") ||
    element.getAttribute("role") === "tooltip" ||
    !!element.closest("#Tooltip-unique") ||
    !!element.closest("#my-react-drawer") ||
    !!element.closest("#tooltip-section-popover") ||
    !!element.closest("#btn-setting-toolbar") ||
    !!element.closest("#button-toolbar") ||
    !!element.closest("#color-picker") ||
    !!element.closest(".qadpt-ext-banner") ||
    !!element.closest("#leftDrawer") ||
    !!element.closest("#image-popover") ||
    !!element.closest("#toggle-fit") ||
    !!element.closest("#color-popover") ||
    !!element.closest("#rte-popover") ||
    !!element.closest("#rte-alignment") ||
    !!element.closest("#rte-alignment-menu") ||
    !!element.closest("#rte-font") ||
    !!element.closest("#rte-bold") ||
    !!element.closest("#rte-italic") ||
    !!element.closest("#rte-underline") ||
    !!element.closest("#rte-strke-through") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#rte-more") ||
    !!element.closest("#rte-text-color") ||
    !!element.closest("#rte-text-color-popover") ||
    !!element.closest("#rte-text-highlight") ||
    !!element.closest("#rte-text-highlight-pop") ||
    !!element.closest("#rte-text-heading") ||
    !!element.closest("#rte-text-heading-menu-items") ||
    !!element.closest("#rte-text-format") ||
    !!element.closest("#rte-text-ul") ||
    !!element.closest("#rte-text-hyperlink") ||
    !!element.closest("#rte-video") ||
    !!element.closest("#rte-clear-formatting") ||
    !!element.closest("#rte-hyperlink-popover") ||
    !!element.closest("#rte-box") ||
    !!element.closest(element.id.startsWith("rt-editor") ? `#${element.id}` : "nope") ||
    !!element.closest("#rte-placeholder") ||
    !!element.closest("#qadpt-designpopup") ||
    !!element.closest("#image-properties") ||
    !!element.closest("#rte-toolbar") ||
    !!element.closest("#tooltipdialog") ||
    !!element.closest("#rte-toolbar-paper") ||
    !!element.closest("#stop-scraping-button-container") ||
    !!element.closest("#rte-alignment-menu") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#quickadapt-scraping-instructions") // Ignore our own instruction banner
  );
};

/**
 * Check if element should be ignored for events
 */
const shouldIgnoreEvents = (element: HTMLElement): boolean => {
  return (
    element.classList.contains("mdc-tooltip__surface") ||
    element.classList.contains("mdc-tooltip__surface-animation") ||
    element.classList.contains("mdc-tooltip") ||
    element.classList.contains("mdc-tooltip--shown") ||
    element.classList.contains("mdc-tooltip--showing") ||
    element.classList.contains("mdc-tooltip--hiding") ||
    element.getAttribute("role") === "tooltip" ||
    !!element.closest("#Tooltip-unique") ||
    !!element.closest("#tooltip-section-popover") ||
    !!element.closest("#btn-setting-toolbar") ||
    !!element.closest("#button-toolbar") ||
    !!element.closest("#color-picker") ||
    !!element.closest(".qadpt-ext-banner") ||
    !!element.closest("#leftDrawer") ||
    !!element.closest("#rte-popover") ||
    !!element.closest("#stop-scraping-button-container") ||
    !!element.closest(element.id.startsWith("rt-editor") ? `#${element.id}` : "nope") ||
    !!element.closest("#rte-box") ||
    !!element.closest("#rte-placeholder") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#qadpt-designpopup") ||
    !!element.closest("#quickadapt-scraping-instructions") // Ignore our own instruction banner
  );
};

/**
 * Add persistent red border to element WITHOUT blocking clicks
 */
const addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {
  if (shouldIgnoreHighlight(element)) return;

  // Add persistent red border
  element.style.outline = '3px solid #ff0000 !important';
  element.style.outlineOffset = '2px';
  element.setAttribute('data-quickadapt-highlighted', 'true');
  _highlightedElements.add(element);

  // No overlay creation - allow clicks to pass through
};

/**
 * Add persistent red border to element and create click-blocking overlay (legacy function)
 */
const addPersistentHighlight = (element: HTMLElement): void => {
  if (shouldIgnoreHighlight(element)) return;

  // Add persistent red border
  element.style.outline = '3px solid #ff0000 !important';
  element.style.outlineOffset = '2px';
  element.setAttribute('data-quickadapt-highlighted', 'true');
  _highlightedElements.add(element);

  // Create click-blocking overlay
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: absolute;
    top: ${element.offsetTop}px;
    left: ${element.offsetLeft}px;
    width: ${element.offsetWidth}px;
    height: ${element.offsetHeight}px;
    background: transparent;
    z-index: 999999;
    pointer-events: auto;
    cursor: not-allowed;
  `;
  overlay.setAttribute('data-quickadapt-overlay', 'true');
  
  const tooltipText = t('Element already scraped click blocked');
  overlay.title = tooltipText;

  // Position overlay relative to the element's parent
  const rect = element.getBoundingClientRect();
  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };

  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;
  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;

  // Add overlay to the element's parent or body
  const parent = element.offsetParent || document.body;
  parent.appendChild(overlay);
  _overlayElements.add(overlay);

  // Block clicks on the overlay
  overlay.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
  }, true);
};

/**
 * Remove all highlights and overlays
 */
const removeAllHighlights = (): void => {
  // Remove highlights
  _highlightedElements.forEach(element => {
    if (element && element.style) {
      element.style.outline = '';
      element.style.outlineOffset = '';
      element.removeAttribute('data-quickadapt-highlighted');
    }
  });
  _highlightedElements.clear();

  // Remove overlays
  _overlayElements.forEach(overlay => {
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay);
    }
  });
  _overlayElements.clear();
};

/**
 * Show brief visual feedback when an element is clicked and scraped
 */
const showClickFeedback = (element: HTMLElement): void => {
  try {
    // Create a temporary feedback indicator
    const feedback = document.createElement('div');
    feedback.style.cssText = `
      position: absolute;
      background: #4CAF50;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10001;
      pointer-events: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      opacity: 0;
      transition: opacity 0.2s ease;
    `;
    
    const scrapedText = `✓ ${t('Scraped')}`;
    feedback.textContent = scrapedText;

    // Position the feedback near the clicked element
    const rect = element.getBoundingClientRect();
    feedback.style.left = `${rect.left + window.scrollX}px`;
    feedback.style.top = `${rect.top + window.scrollY - 30}px`;

    document.body.appendChild(feedback);

    // Animate in
    setTimeout(() => {
      feedback.style.opacity = '1';
    }, 10);

    // Remove after 2 seconds
    setTimeout(() => {
      feedback.style.opacity = '0';
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback);
        }
      }, 200);
    }, 2000);
  } catch (error) {
  }
};

/**
 * Extract data from a single element (optimized for click-based scraping)
 */
const extractElementData = (element: HTMLElement): ElementData => {
  const rect = element.getBoundingClientRect();
  const xpath = generateXPath(element);
  const cssSelector = generateCssSelector(element);

  return {
    tagName: element.tagName,
    id: element.id || '',
    className: getElementClassName(element),
    text: element.textContent?.trim() || '',
    attributes: Array.from(element.attributes).reduce((acc, attr) => {
      acc[attr.name] = attr.value;
      return acc;
    }, {} as Record<string, string>),
    xpath,
    cssSelector,
    rect: {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height
    },
    children: [], // We don't need children for click-based scraping
    isVisible: rect.width > 0 && rect.height > 0,
    timestamp: new Date().toISOString(),
    url: window.location.href // Add URL to each element
  };
};

/**
 * Handle click events for element scraping
 */
const handleElementClick = (event: MouseEvent): void => {
  try {
    // IMPORTANT: Don't prevent default or stop propagation
    // This allows the original click functionality to work normally
    // (navigation, form submission, button clicks, etc.)

    const target = event.target as HTMLElement;
    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {
      return;
    }

    if (shouldIgnoreEvents(target)) {
      return;
    }

    if (target.hasAttribute('data-quickadapt-highlighted')) {
      return; 
    }


    // Extract data from clicked element ONLY
    const clickedElementData = extractElementData(target);

    // Store only the clicked element data (no parent element)
    const elementsToStore = [clickedElementData];

    // Add to scraped data
    setScrapedData({ elements: elementsToStore }, true);

    // Add persistent red border WITHOUT blocking clicks (only to clicked element)
    addPersistentHighlightWithoutBlocking(target);


    // // Show brief success feedback
    // showClickFeedback(target);


  } catch (error) {
  }
};

export const setScrapedData = (data: any, append: boolean = false): void => {
  const timestamp = new Date().toISOString();
  _lastScrapedTimestamp = timestamp;

  if (!append) {
    // Clear existing data if not appending
    _scrapedData = [];
    _elementMap.clear();
  }

  // Process each element in the data
  if (data && data.elements && Array.isArray(data.elements)) {
    data.elements.forEach((element: ElementData) => {
      // Add timestamp to the element
      element.timestamp = timestamp;

      // Use XPath as a unique identifier for the element
      if (element.xpath) {
        // If element already exists in the map, don't add it again (prevent duplicates)
        if (_elementMap.has(element.xpath)) {
          return; // Skip this element
        } else {
          // New element, add to map and data array
          _elementMap.set(element.xpath, element);
          _scrapedData.push(element);
        
        }
      } else {
        // No XPath, check for duplicates by other means (tagName + id + className)
        const isDuplicate = _scrapedData.some(existing =>
          existing.tagName === element.tagName &&
          existing.id === element.id &&
          existing.className === element.className
        );

        if (!isDuplicate) {
          _scrapedData.push(element);

        } else {

        }
      }
    });
  }
};

/**
 * Get the currently scraped data
 */
export const getScrapedData = (): any[] => {
  return _scrapedData;
};

/**
 * Get element by XPath
 */
export const getElementByXPath = (xpath: string): ElementData | undefined => {
  return _elementMap.get(xpath);
};



/**
 * Get all xpath data from scraped elements
 */
export const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {
  return _scrapedData.map(element => ({
    xpath: element.xpath,
    tagName: element.tagName,
    id: element.id,
    className: element.className,
    text: element.text,
    timestamp: element.timestamp,
    url: element.url
  }));
};

/**
 * Manually send current scraped data to backend API (can be called independently)
 */
export const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {
  try {
    if (_scrapedData.length === 0) {
      // Try to load from storage if no current data
      const storedData = await loadScrapedDataFromStorage();
      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {
        // alert('No scraped data available to send to API. Please scrape some elements first.');
        return;
      }
      // Use stored data for API call
      await saveScrapedDataToFile(accountId);
    } else {
      // Save current data to storage first, then send to API
      await saveScrapedDataToStorage();
      await saveScrapedDataToFile(accountId);
    }
  } catch (error) {
    // alert('Error sending scraped data to backend API. Check console for details.');
  }
};

/**
 * Get scraped data count
 */
export const getScrapedDataCount = (): number => {
  return _scrapedData.length;
};

/**
 * Get the timestamp of the last scrape
 */
export const getLastScrapedTimestamp = (): string => {
  return _lastScrapedTimestamp;
};

/**
 * Clear scraped data
 */
export const clearScrapedData = (): void => {
  _scrapedData = [];
  _lastScrapedTimestamp = '';
};

/**
 * Save scraped data to Chrome storage
 */
export const saveScrapedDataToStorage = async (): Promise<void> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      const storageData = {
        scrapedData: _scrapedData,
        timestamp: _lastScrapedTimestamp,
        url: window.location.href,
        title: document.title,
        elementCount: _scrapedData.length,
        xpathData: _scrapedData.map(element => ({
          xpath: element.xpath,
          tagName: element.tagName,
          id: element.id,
          className: element.className,
          text: element.text,
          attributes: element.attributes,
          timestamp: element.timestamp,
          url: element.url
        }))
      };

      await chrome.storage.local.set({
        'quickadapt-scraped-data': storageData
      });
   storageData.xpathData.forEach((item, index) => {
        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);
      });
    } else {
      // Fallback: save to localStorage
      const storageData = {
        scrapedData: _scrapedData,
        timestamp: _lastScrapedTimestamp,
        url: window.location.href,
        title: document.title,
        elementCount: _scrapedData.length,
        xpathData: _scrapedData.map(element => ({
          xpath: element.xpath,
          tagName: element.tagName,
          id: element.id,
          className: element.className,
          text: element.text,
          attributes: element.attributes,
          timestamp: element.timestamp,
          url: element.url
        }))
      };
      localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));
    }
  } catch (error) {
  }
};

/**
 * Load scraped data from Chrome storage
 */
export const loadScrapedDataFromStorage = async (): Promise<any> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      const result = await chrome.storage.local.get('quickadapt-scraped-data');
      return result['quickadapt-scraped-data'] || null;
    } else {
      // Fallback: load from localStorage
      const data = localStorage.getItem('quickadapt-scraped-data');
      return data ? JSON.parse(data) : null;
    }
  } catch (error) {
    return null;
  }
};

/**
 * Send scraped data from Chrome storage to backend API
 */
export const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {
  try {

    const storedData = await loadScrapedDataFromStorage();

    if (!storedData) {
      return;
    }

    const apiData = {
      metadata: {
        url: storedData.url || window.location.href,
        title: storedData.title || document.title,
        timestamp: storedData.timestamp || new Date().toISOString(),
        elementCount: storedData.elementCount || 0,
        exportedAt: new Date().toISOString()
      },
      elements: storedData.scrapedData || [],
      xpathData: storedData.xpathData || []
    };


    // Send data to backend API
    await uploadXPathsFile(apiData, accountId);

  } catch (error) {
  }
};

/**
 * Upload XPath data to backend API using existing FileService
 */
export const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {
  try {

    // Convert JSON data to FormData as expected by the existing API
    const formData = new FormData();

    // Create a JSON file blob
    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');
    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;

    // Add the file to FormData
    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name

    // Add metadata as form fields if needed
    formData.append('elementCount', data.metadata.elementCount.toString());
    formData.append('url', data.metadata.url);
    formData.append('timestamp', data.metadata.timestamp);

   

    // Import and use the existing uploadXpathsFile function
    const { uploadXpathsFile } = await import('./FileService');

    if (!accountId) {
      throw new Error('Account ID is required to upload XPath data');
    }

    const response = await uploadXpathsFile(accountId, formData);

  } catch (error) {

    // Show error message to user
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    throw error; // Re-throw to be caught by calling function
  }
};

/**
 * Start click-based scraping process
 */
export const startScraping = (): void => {
  if (_isScrapingActive) return;

  _isScrapingActive = true;
  clearScrapedData();


  // Add click event listener to capture element clicks
  if (!_clickListener) {
    _clickListener = handleElementClick;
    document.addEventListener('click', _clickListener, true); // Use capture phase
  }

  // Send message to content script to enable click-based scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'startClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));
    }
  }

  // Show user instruction with translation support
  showScrapingInstructions();
};

/**
 * Stop click-based scraping process
 */
export const stopScraping = async (accountId: string): Promise<void> => {
  if (!_isScrapingActive) return;

  _isScrapingActive = false;


  // Remove click event listener
  if (_clickListener) {
    document.removeEventListener('click', _clickListener, true);
    _clickListener = null;
  }

  // Save scraped data to Chrome storage
  if (_scrapedData.length > 0) {
    await saveScrapedDataToStorage();

    // Get data from Chrome storage and save to file
    await saveScrapedDataToFile(accountId);
  }

  // Remove all highlights and overlays
  removeAllHighlights();

  // Send message to background script to stop scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'stopClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));
    }
  }

  // Hide instructions
  hideScrapingInstructions();
};

/**
 * Show scraping instructions to user
 */
const showScrapingInstructions = (): void => {
  // Remove existing instruction if any
  hideScrapingInstructions();

  const instructionDiv = document.createElement('div');
  instructionDiv.id = 'quickadapt-scraping-instructions';
  instructionDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    max-width: 320px;
    text-align: center;
  `;
  
  // Use translations with i18n instance directly
  const mainTitle = `🎯 ${t('Click Scraping Active')}`;
  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;
  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;
  const originalClick = `• ${t('Original click functionality still works')}`;
  const redBorders = `• ${t('Red borders show scraped elements')}`;
  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;
  
  instructionDiv.innerHTML = `
    ${mainTitle}<br>
    <small style="font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;">
      ${clickElement}<br>
      ${onlyClicked}<br>
      ${originalClick}<br>
      ${redBorders}<br>
      ${dataSaved}
    </small>
  `;

  document.body.appendChild(instructionDiv);

  // Auto-hide after 8 seconds
  setTimeout(() => {
    if (instructionDiv.parentNode) {
      instructionDiv.style.opacity = '0.7';
    }
  }, 8000);
};

/**
 * Hide scraping instructions
 */
const hideScrapingInstructions = (): void => {
  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');
  if (existingInstruction) {
    existingInstruction.remove();
  }
};


/**
 * Initialize click-based scraping service
 * This should be called when the extension is loaded
 */
export const initScrapingService = (): void => {
  _isScrapingActive = false;
  _scrapedData = [];
  _elementMap.clear();
  _lastScrapedTimestamp = '';
  _clickListener = null;

  // Check if we're in a Chrome extension environment
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      if (message.action === 'updateScrapingState') {
        _isScrapingActive = message.isActive;
        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'getScrapingState') {
        sendResponse({
          isActive: _isScrapingActive,
          lastTimestamp: _lastScrapedTimestamp,
          elementCount: _scrapedData.length
        });
        return true;
      }

      if (message.action === 'getScrapedData') {
        sendResponse({
          data: _scrapedData,
          timestamp: _lastScrapedTimestamp
        });
        return true;
      }

      if (message.action === 'clearScrapedData') {
        clearScrapedData();
        sendResponse({ success: true });
        return true;
      }
    });
  } else {
  }
};


// Initialize the service
initScrapingService();
